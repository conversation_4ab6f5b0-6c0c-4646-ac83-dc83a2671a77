# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Video/Audio files (output)
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.mkv
*.webm
*.m4v
*.3gp
*.mp3
*.wav
*.flac
*.aac
*.ogg
*.wma
*.m4a

# Image files (generated)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.webp
*.svg
*.ico
*.psd
*.ai

# Temporary files
temp/
tmp/
*.tmp
*.temp
*.log
*.lock
*.pid
*.seed
*.coverage
*.nyc_output

# Output directories
output/
exports/
renders/
generated/
build/
dist/

# Cache directories
cache/
.cache/
__cache__/
.moviepy_cache/
.ffmpeg_cache/

# Config files (sensitive)
config.local.json
config.secret.json
.secrets
*.key
*.pem
*.p12
*.pfx
credentials.json

# Development files
test_*.py
debug_*.py
temp_*.py
scratch_*.py
experiment_*.py
playground_*.py

# Log files
*.log
logs/
log/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old
*.orig
*.save
*~

# Archive files
*.zip
*.tar
*.tar.gz
*.tar.bz2
*.tar.xz
*.rar
*.7z

# Font files (if not distributed)
*.ttf
*.otf
*.woff
*.woff2
*.eot

# Node.js (if any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn/

# Docker
Dockerfile.*
docker-compose.override.yml
.dockerignore

# Git
.git/
.gitmodules

# Project specific
# Keep example configs but ignore runtime configs
protocol.local.json
config.local.json
user_config.json
runtime_config.json

# Keep template configs but ignore user templates
templates/user_*
templates/custom_*

# Documentation builds
docs/_build/
docs/build/

# Performance profiling
*.prof
*.profile
cProfile.out
profile.out 