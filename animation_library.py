"""
🎬 Reavo Video Creator - 专业动画库
Professional Animation Library for Real Estate and Marketing Videos

提供分层的动画系统：
1. 基础动画效果 (Basic Animations)
2. 组合动画效果 (Composite Animations) 
3. 场景化预设 (Scene Presets)
4. 专业房地产动画 (Real Estate Animations)
"""

from typing import Dict, Any, List, Callable, Tuple
import numpy as np
from dataclasses import dataclass
from enum import Enum
import math

class AnimationCategory(Enum):
    """动画分类"""
    BASIC = "basic"              # 基础动画
    COMPOSITE = "composite"      # 组合动画
    REAL_ESTATE = "real_estate"  # 房地产专业动画
    MARKETING = "marketing"      # 营销动画
    CINEMATIC = "cinematic"      # 电影级动画

class EasingType(Enum):
    """缓动函数类型"""
    LINEAR = "linear"
    EASE_IN = "ease_in"
    EASE_OUT = "ease_out"
    EASE_IN_OUT = "ease_in_out"
    BOUNCE = "bounce"
    ELASTIC = "elastic"
    BACK = "back"

@dataclass
class AnimationConfig:
    """动画配置数据类"""
    name: str
    category: AnimationCategory
    description: str
    duration_range: Tuple[float, float]  # 推荐时长范围
    parameters: Dict[str, Any]
    easing: EasingType = EasingType.EASE_IN_OUT
    intensity: float = 1.0  # 强度系数 0.1-2.0

class EasingFunctions:
    """缓动函数库"""
    
    @staticmethod
    def linear(t: float) -> float:
        return t
    
    @staticmethod
    def ease_in_quad(t: float) -> float:
        return t * t
    
    @staticmethod
    def ease_out_quad(t: float) -> float:
        return 1 - (1 - t) * (1 - t)
    
    @staticmethod
    def ease_in_out_quad(t: float) -> float:
        return 2 * t * t if t < 0.5 else 1 - pow(-2 * t + 2, 2) / 2
    
    @staticmethod
    def ease_in_cubic(t: float) -> float:
        return t * t * t
    
    @staticmethod
    def ease_out_cubic(t: float) -> float:
        return 1 - pow(1 - t, 3)
    
    @staticmethod
    def ease_in_out_cubic(t: float) -> float:
        return 4 * t * t * t if t < 0.5 else 1 - pow(-2 * t + 2, 3) / 2
    
    @staticmethod
    def bounce_out(t: float) -> float:
        n1 = 7.5625
        d1 = 2.75
        if t < 1 / d1:
            return n1 * t * t
        elif t < 2 / d1:
            t -= 1.5 / d1
            return n1 * t * t + 0.75
        elif t < 2.5 / d1:
            t -= 2.25 / d1
            return n1 * t * t + 0.9375
        else:
            t -= 2.625 / d1
            return n1 * t * t + 0.984375
    
    @staticmethod
    def elastic_out(t: float) -> float:
        c4 = (2 * math.pi) / 3
        return 0 if t == 0 else 1 if t == 1 else pow(2, -10 * t) * math.sin((t * 10 - 0.75) * c4) + 1

class RealEstateAnimations:
    """房地产专业动画库"""
    
    @staticmethod
    def ken_burns_effect(intensity: float = 1.0) -> AnimationConfig:
        """Ken Burns 效果 - 经典的纪录片风格缩放平移"""
        return AnimationConfig(
            name="ken_burns",
            category=AnimationCategory.REAL_ESTATE,
            description="经典Ken Burns效果，缓慢缩放配合平移，营造专业纪录片感",
            duration_range=(4.0, 8.0),
            parameters={
                "zoom_start": 1.0,
                "zoom_end": 1.15 * intensity,
                "pan_x": 20 * intensity,
                "pan_y": -15 * intensity,
                "brightness_boost": 1.05,
                "contrast_enhance": 1.1
            },
            easing=EasingType.EASE_IN_OUT,
            intensity=intensity
        )
    
    @staticmethod
    def luxury_reveal(intensity: float = 1.0) -> AnimationConfig:
        """豪华揭示效果 - 适合高端房产展示"""
        return AnimationConfig(
            name="luxury_reveal",
            category=AnimationCategory.REAL_ESTATE,
            description="豪华房产展示效果，结合缩放、亮度和对比度变化",
            duration_range=(3.0, 6.0),
            parameters={
                "zoom_start": 1.1,
                "zoom_end": 1.0,
                "brightness_start": 0.8,
                "brightness_end": 1.1,
                "contrast_start": 0.9,
                "contrast_end": 1.2,
                "saturation_boost": 1.15
            },
            easing=EasingType.EASE_OUT,
            intensity=intensity
        )
    
    @staticmethod
    def parallax_scroll(direction: str = "up", intensity: float = 1.0) -> AnimationConfig:
        """视差滚动效果 - 创造深度感"""
        pan_values = {
            "up": (0, -30 * intensity),
            "down": (0, 30 * intensity),
            "left": (-30 * intensity, 0),
            "right": (30 * intensity, 0)
        }
        
        return AnimationConfig(
            name=f"parallax_scroll_{direction}",
            category=AnimationCategory.REAL_ESTATE,
            description=f"视差滚动效果 - {direction}方向，创造空间深度感",
            duration_range=(4.0, 7.0),
            parameters={
                "zoom_start": 1.0,
                "zoom_end": 1.08 * intensity,
                "pan_x": pan_values[direction][0],
                "pan_y": pan_values[direction][1],
                "brightness_curve": "subtle_boost"
            },
            easing=EasingType.LINEAR,
            intensity=intensity
        )
    
    @staticmethod
    def architectural_focus(intensity: float = 1.0) -> AnimationConfig:
        """建筑焦点效果 - 突出建筑细节"""
        return AnimationConfig(
            name="architectural_focus",
            category=AnimationCategory.REAL_ESTATE,
            description="建筑细节聚焦效果，适合展示设计特色",
            duration_range=(3.0, 5.0),
            parameters={
                "zoom_start": 1.0,
                "zoom_end": 1.25 * intensity,
                "sharpness_boost": 1.2,
                "contrast_enhance": 1.15,
                "vignette_strength": 0.3
            },
            easing=EasingType.EASE_IN_OUT,
            intensity=intensity
        )

class CinematicAnimations:
    """电影级动画效果"""
    
    @staticmethod
    def dramatic_zoom(intensity: float = 1.0) -> AnimationConfig:
        """戏剧性缩放 - 电影级视觉冲击"""
        return AnimationConfig(
            name="dramatic_zoom",
            category=AnimationCategory.CINEMATIC,
            description="戏剧性缩放效果，创造强烈视觉冲击",
            duration_range=(2.0, 4.0),
            parameters={
                "zoom_start": 1.0,
                "zoom_end": 1.4 * intensity,
                "rotation_subtle": 2.0,
                "brightness_dramatic": 1.2,
                "contrast_boost": 1.3
            },
            easing=EasingType.EASE_IN,
            intensity=intensity
        )
    
    @staticmethod
    def dolly_zoom(intensity: float = 1.0) -> AnimationConfig:
        """推拉镜头效果 - 希区柯克式变焦"""
        return AnimationConfig(
            name="dolly_zoom",
            category=AnimationCategory.CINEMATIC,
            description="经典推拉镜头效果，营造紧张或震撼感",
            duration_range=(3.0, 6.0),
            parameters={
                "zoom_start": 1.2,
                "zoom_end": 0.9,
                "perspective_shift": True,
                "focal_length_simulation": True
            },
            easing=EasingType.LINEAR,
            intensity=intensity
        )

class AnimationRenderer:
    """动画渲染引擎"""

    @staticmethod
    def create_animation_function(config: AnimationConfig, duration: float) -> Dict[str, Callable]:
        """根据动画配置创建MoviePy动画函数"""
        params = config.parameters
        easing_func = AnimationRenderer._get_easing_function(config.easing)

        animation_functions = {}

        # 缩放动画
        if "zoom_start" in params and "zoom_end" in params:
            def zoom_animation(t):
                progress = easing_func(t / duration)
                zoom_start = params["zoom_start"]
                zoom_end = params["zoom_end"]
                return zoom_start + (zoom_end - zoom_start) * progress
            animation_functions["zoom"] = zoom_animation

        # 平移动画
        if "pan_x" in params or "pan_y" in params:
            def pan_animation(t):
                progress = easing_func(t / duration)
                pan_x = params.get("pan_x", 0) * progress
                pan_y = params.get("pan_y", 0) * progress
                return (pan_x, pan_y)
            animation_functions["pan"] = pan_animation

        # 亮度动画
        if "brightness_start" in params and "brightness_end" in params:
            def brightness_animation(t):
                progress = easing_func(t / duration)
                brightness_start = params["brightness_start"]
                brightness_end = params["brightness_end"]
                return brightness_start + (brightness_end - brightness_start) * progress
            animation_functions["brightness"] = brightness_animation
        elif "brightness_boost" in params:
            def brightness_boost_animation(t):
                progress = easing_func(t / duration)
                boost = params["brightness_boost"]
                return 1.0 + (boost - 1.0) * progress
            animation_functions["brightness"] = brightness_boost_animation

        # 对比度动画
        if "contrast_start" in params and "contrast_end" in params:
            def contrast_animation(t):
                progress = easing_func(t / duration)
                contrast_start = params["contrast_start"]
                contrast_end = params["contrast_end"]
                return contrast_start + (contrast_end - contrast_start) * progress
            animation_functions["contrast"] = contrast_animation
        elif "contrast_enhance" in params:
            def contrast_enhance_animation(t):
                progress = easing_func(t / duration)
                enhance = params["contrast_enhance"]
                return 1.0 + (enhance - 1.0) * progress
            animation_functions["contrast"] = contrast_enhance_animation

        # 饱和度动画
        if "saturation_boost" in params:
            def saturation_animation(t):
                progress = easing_func(t / duration)
                boost = params["saturation_boost"]
                return 1.0 + (boost - 1.0) * progress
            animation_functions["saturation"] = saturation_animation

        # 旋转动画
        if "rotation_subtle" in params:
            def rotation_animation(t):
                progress = easing_func(t / duration)
                rotation = params["rotation_subtle"] * progress
                return rotation
            animation_functions["rotation"] = rotation_animation

        return animation_functions

    @staticmethod
    def _get_easing_function(easing_type: EasingType) -> Callable:
        """获取缓动函数"""
        easing_map = {
            EasingType.LINEAR: EasingFunctions.linear,
            EasingType.EASE_IN: EasingFunctions.ease_in_cubic,
            EasingType.EASE_OUT: EasingFunctions.ease_out_cubic,
            EasingType.EASE_IN_OUT: EasingFunctions.ease_in_out_cubic,
            EasingType.BOUNCE: EasingFunctions.bounce_out,
            EasingType.ELASTIC: EasingFunctions.elastic_out
        }
        return easing_map.get(easing_type, EasingFunctions.ease_in_out_cubic)

class AnimationLibrary:
    """动画库主类"""

    def __init__(self):
        self.animations = {}
        self.renderer = AnimationRenderer()
        self._register_animations()

    def _register_animations(self):
        """注册所有动画效果"""
        # 房地产动画
        self.animations["ken_burns"] = RealEstateAnimations.ken_burns_effect
        self.animations["luxury_reveal"] = RealEstateAnimations.luxury_reveal
        self.animations["parallax_up"] = lambda i=1.0: RealEstateAnimations.parallax_scroll("up", i)
        self.animations["parallax_down"] = lambda i=1.0: RealEstateAnimations.parallax_scroll("down", i)
        self.animations["parallax_left"] = lambda i=1.0: RealEstateAnimations.parallax_scroll("left", i)
        self.animations["parallax_right"] = lambda i=1.0: RealEstateAnimations.parallax_scroll("right", i)
        self.animations["architectural_focus"] = RealEstateAnimations.architectural_focus

        # 电影级动画
        self.animations["dramatic_zoom"] = CinematicAnimations.dramatic_zoom
        self.animations["dolly_zoom"] = CinematicAnimations.dolly_zoom

    def get_animation(self, name: str, intensity: float = 1.0) -> AnimationConfig:
        """获取动画配置"""
        if name not in self.animations:
            raise ValueError(f"动画 '{name}' 不存在")
        return self.animations[name](intensity)

    def get_animation_functions(self, name: str, duration: float, intensity: float = 1.0) -> Dict[str, Callable]:
        """获取可用于MoviePy的动画函数"""
        config = self.get_animation(name, intensity)
        return self.renderer.create_animation_function(config, duration)

    def get_real_estate_presets(self) -> List[str]:
        """获取房地产专用动画预设"""
        return [
            "ken_burns", "luxury_reveal", "parallax_up", "parallax_down",
            "parallax_left", "parallax_right", "architectural_focus"
        ]

    def get_cinematic_presets(self) -> List[str]:
        """获取电影级动画预设"""
        return ["dramatic_zoom", "dolly_zoom"]

    def suggest_animation_for_scene(self, scene_type: str, duration: float) -> str:
        """根据场景类型和时长推荐动画"""
        suggestions = {
            "exterior": "ken_burns" if duration > 4 else "luxury_reveal",
            "interior": "parallax_up" if duration > 5 else "architectural_focus",
            "pool": "parallax_down",
            "bedroom": "luxury_reveal",
            "kitchen": "architectural_focus",
            "living_room": "ken_burns",
            "garden": "parallax_left",
            "view": "dramatic_zoom"
        }
        return suggestions.get(scene_type, "ken_burns")

    def get_animation_description(self, name: str) -> str:
        """获取动画效果描述"""
        try:
            config = self.get_animation(name)
            return f"{config.name}: {config.description}"
        except ValueError:
            return f"未知动画: {name}"

# 全局动画库实例
animation_library = AnimationLibrary()
