"""
🎬 Animation CLI - 动画预设命令行工具
Command Line Interface for Animation Presets

提供动画预设的查看、推荐和配置生成功能
"""

import argparse
from typing import Dict, Any
from enhanced_animation_renderer import get_animation_presets_info, enhanced_animation_renderer

def print_animation_presets():
    """打印所有可用的动画预设"""
    info = get_animation_presets_info()
    
    print("🎬 Reavo Video Creator - 专业动画预设库")
    print("=" * 60)
    
    # 打印分类信息
    for category, presets in info["categories"].items():
        category_name = {
            "real_estate": "🏠 房地产专业动画",
            "cinematic": "🎬 电影级动画"
        }.get(category, f"📁 {category}")
        
        print(f"\n{category_name}")
        print("-" * 40)
        
        for preset, description in presets.items():
            print(f"  📌 {preset}")
            print(f"     {description}")
    
    # 打印场景推荐
    print(f"\n🎯 场景智能推荐")
    print("-" * 40)
    for scene, suggestion in info["scene_suggestions"].items():
        print(f"  🏷️  {scene:12} → {suggestion}")

def print_preset_details(preset_name: str):
    """打印特定预设的详细信息"""
    try:
        config = enhanced_animation_renderer.animation_lib.get_animation(preset_name)
        
        print(f"🎬 动画预设详情: {preset_name}")
        print("=" * 50)
        print(f"📝 名称: {config.name}")
        print(f"📂 分类: {config.category.value}")
        print(f"📖 描述: {config.description}")
        print(f"⏱️  推荐时长: {config.duration_range[0]}-{config.duration_range[1]}秒")
        print(f"🎚️  缓动类型: {config.easing.value}")
        print(f"💪 默认强度: {config.intensity}")
        
        print(f"\n🔧 参数配置:")
        for param, value in config.parameters.items():
            print(f"  • {param}: {value}")
        
        print(f"\n📋 配置示例:")
        print(f'  "animation": {{')
        print(f'    "preset": "{preset_name}",')
        print(f'    "intensity": 1.0,')
        print(f'    "scene_type": "exterior"')
        print(f'  }}')
        
    except ValueError:
        print(f"❌ 动画预设 '{preset_name}' 不存在")
        print("💡 使用 'python animation_cli.py --list' 查看所有可用预设")

def suggest_animation(scene_type: str, duration: float):
    """为场景推荐动画"""
    suggestion = enhanced_animation_renderer.suggest_preset_for_scene(scene_type, duration)
    
    print(f"🎯 场景动画推荐")
    print("=" * 30)
    print(f"🏷️  场景类型: {scene_type}")
    print(f"⏱️  视频时长: {duration}秒")
    print(f"🎬 推荐动画: {suggestion}")
    
    # 显示推荐动画的详细信息
    print(f"\n📖 推荐理由:")
    description = enhanced_animation_renderer.get_preset_description(suggestion)
    print(f"  {description}")
    
    print(f"\n📋 配置示例:")
    print(f'  "animation": {{')
    print(f'    "preset": "{suggestion}",')
    print(f'    "intensity": 1.0,')
    print(f'    "scene_type": "{scene_type}"')
    print(f'  }}')

def generate_config_template(preset_name: str, scene_type: str = "general"):
    """生成配置模板"""
    try:
        config = enhanced_animation_renderer.animation_lib.get_animation(preset_name)
        
        template = {
            "background": {
                "type": "image",
                "url": "your_image_url_here.jpg",
                "animation": {
                    "preset": preset_name,
                    "intensity": 1.0,
                    "scene_type": scene_type
                }
            }
        }
        
        print(f"📋 {preset_name} 配置模板")
        print("=" * 40)
        print("```json")
        import json
        print(json.dumps(template, indent=2, ensure_ascii=False))
        print("```")
        
        print(f"\n💡 使用提示:")
        print(f"  • 将 'your_image_url_here.jpg' 替换为实际图片URL")
        print(f"  • intensity 范围: 0.1-2.0 (1.0为标准强度)")
        print(f"  • scene_type 影响动画的细节调整")
        
    except ValueError:
        print(f"❌ 动画预设 '{preset_name}' 不存在")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="🎬 Reavo Video Creator - 动画预设工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python animation_cli.py --list                    # 查看所有动画预设
  python animation_cli.py --detail ken_burns        # 查看特定预设详情
  python animation_cli.py --suggest exterior 5.0    # 获取场景推荐
  python animation_cli.py --template ken_burns      # 生成配置模板
        """
    )
    
    parser.add_argument(
        "--list", "-l",
        action="store_true",
        help="显示所有可用的动画预设"
    )
    
    parser.add_argument(
        "--detail", "-d",
        metavar="PRESET_NAME",
        help="显示特定动画预设的详细信息"
    )
    
    parser.add_argument(
        "--suggest", "-s",
        nargs=2,
        metavar=("SCENE_TYPE", "DURATION"),
        help="为场景类型和时长推荐动画 (例: exterior 5.0)"
    )
    
    parser.add_argument(
        "--template", "-t",
        metavar="PRESET_NAME",
        help="生成指定预设的配置模板"
    )
    
    parser.add_argument(
        "--scene-type",
        default="general",
        help="场景类型 (用于模板生成，默认: general)"
    )
    
    args = parser.parse_args()
    
    if args.list:
        print_animation_presets()
    elif args.detail:
        print_preset_details(args.detail)
    elif args.suggest:
        scene_type, duration_str = args.suggest
        try:
            duration = float(duration_str)
            suggest_animation(scene_type, duration)
        except ValueError:
            print("❌ 时长必须是数字")
    elif args.template:
        generate_config_template(args.template, args.scene_type)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
