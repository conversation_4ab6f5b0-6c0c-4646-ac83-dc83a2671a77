"""
🎬 Animation Demo - 动画效果演示和测试
Animation Effects Demonstration and Testing

演示新动画库的功能和效果对比
"""

from animation_library import animation_library, AnimationCategory
from enhanced_animation_renderer import enhanced_animation_renderer
import json

def demo_animation_library():
    """演示动画库功能"""
    print("🎬 Reavo Video Creator - 专业动画库演示")
    print("=" * 60)
    
    # 1. 展示房地产动画预设
    print("\n🏠 房地产专业动画预设")
    print("-" * 40)
    
    real_estate_presets = animation_library.get_real_estate_presets()
    for preset in real_estate_presets:
        config = animation_library.get_animation(preset)
        print(f"📌 {preset}")
        print(f"   {config.description}")
        print(f"   推荐时长: {config.duration_range[0]}-{config.duration_range[1]}秒")
        print()
    
    # 2. 展示电影级动画预设
    print("\n🎬 电影级动画预设")
    print("-" * 40)
    
    cinematic_presets = animation_library.get_cinematic_presets()
    for preset in cinematic_presets:
        config = animation_library.get_animation(preset)
        print(f"📌 {preset}")
        print(f"   {config.description}")
        print(f"   推荐时长: {config.duration_range[0]}-{config.duration_range[1]}秒")
        print()

def demo_scene_suggestions():
    """演示场景推荐功能"""
    print("\n🎯 智能场景推荐演示")
    print("=" * 40)
    
    test_scenes = [
        ("exterior", 5.0),
        ("interior", 4.5),
        ("pool", 6.0),
        ("bedroom", 3.5),
        ("kitchen", 4.0),
        ("living_room", 5.5),
        ("garden", 6.5),
        ("view", 3.0)
    ]
    
    for scene_type, duration in test_scenes:
        suggestion = animation_library.suggest_animation_for_scene(scene_type, duration)
        config = animation_library.get_animation(suggestion)
        print(f"🏷️  {scene_type:12} ({duration}s) → {suggestion}")
        print(f"    {config.description}")
        print()

def demo_animation_functions():
    """演示动画函数生成"""
    print("\n🔧 动画函数生成演示")
    print("=" * 40)
    
    # 测试Ken Burns效果
    print("📌 Ken Burns 效果函数:")
    functions = animation_library.get_animation_functions("ken_burns", 5.0, 1.2)
    
    for func_name, func in functions.items():
        print(f"  • {func_name}: {type(func).__name__}")
        # 测试函数在不同时间点的值
        if func_name == "zoom":
            print(f"    t=0.0: {func(0.0):.3f}")
            print(f"    t=2.5: {func(2.5):.3f}")
            print(f"    t=5.0: {func(5.0):.3f}")
        elif func_name == "pan":
            print(f"    t=0.0: {func(0.0)}")
            print(f"    t=2.5: {func(2.5)}")
            print(f"    t=5.0: {func(5.0)}")
        print()

def demo_config_comparison():
    """演示配置对比"""
    print("\n📊 配置对比演示")
    print("=" * 40)
    
    # 原有配置
    old_config = {
        "animation": {
            "zoom": 1.02,
            "brightness": 0.95,
            "contrast": 1.05
        }
    }
    
    # 新配置 - 预设模式
    new_config_preset = {
        "animation": {
            "preset": "ken_burns",
            "intensity": 1.2,
            "scene_type": "exterior"
        }
    }
    
    # 新配置 - 自定义模式
    new_config_custom = {
        "animation": {
            "custom": {
                "zoom": 1.15,
                "pan": {"x": 20, "y": -15},
                "brightness": 1.05,
                "contrast": 1.1,
                "easing": "ease_in_out"
            }
        }
    }
    
    print("❌ 原有配置 (效果微弱):")
    print(json.dumps(old_config, indent=2, ensure_ascii=False))
    
    print("\n✅ 新配置 - 预设模式 (简单易用):")
    print(json.dumps(new_config_preset, indent=2, ensure_ascii=False))
    
    print("\n🎨 新配置 - 自定义模式 (专业用户):")
    print(json.dumps(new_config_custom, indent=2, ensure_ascii=False))

def demo_intensity_effects():
    """演示强度参数效果"""
    print("\n💪 强度参数效果演示")
    print("=" * 40)
    
    intensities = [0.5, 1.0, 1.5, 2.0]
    
    for intensity in intensities:
        config = animation_library.get_animation("ken_burns", intensity)
        params = config.parameters
        
        print(f"🎚️  强度 {intensity}:")
        print(f"   缩放: {params['zoom_start']} → {params['zoom_end']}")
        print(f"   平移: ({params['pan_x']}, {params['pan_y']})")
        print(f"   亮度: {params['brightness_boost']}")
        print()

def demo_real_estate_showcase():
    """演示房地产展示的完整动画方案"""
    print("\n🏠 房地产展示完整方案")
    print("=" * 50)
    
    # 房地产视频的典型场景序列
    scenes = [
        {"type": "exterior", "duration": 5.0, "description": "建筑外观"},
        {"type": "interior", "duration": 4.5, "description": "客厅空间"},
        {"type": "kitchen", "duration": 4.0, "description": "厨房细节"},
        {"type": "bedroom", "duration": 3.5, "description": "主卧室"},
        {"type": "pool", "duration": 6.0, "description": "泳池区域"},
        {"type": "garden", "duration": 5.5, "description": "花园景观"},
        {"type": "view", "duration": 3.0, "description": "景观视野"}
    ]
    
    total_duration = 0
    
    for i, scene in enumerate(scenes, 1):
        scene_type = scene["type"]
        duration = scene["duration"]
        description = scene["description"]
        
        # 获取推荐动画
        suggested = animation_library.suggest_animation_for_scene(scene_type, duration)
        config = animation_library.get_animation(suggested)
        
        print(f"🎬 场景 {i}: {description} ({duration}s)")
        print(f"   推荐动画: {suggested}")
        print(f"   效果描述: {config.description}")
        print(f"   配置示例:")
        print(f'   "animation": {{')
        print(f'     "preset": "{suggested}",')
        print(f'     "intensity": 1.0,')
        print(f'     "scene_type": "{scene_type}"')
        print(f'   }}')
        print()
        
        total_duration += duration
    
    print(f"📊 总时长: {total_duration}秒")
    print(f"🎯 视觉效果: 专业房地产宣传片级别")

def main():
    """主演示函数"""
    print("🎬 启动动画库演示...")
    print()
    
    # 运行所有演示
    demo_animation_library()
    demo_scene_suggestions()
    demo_animation_functions()
    demo_config_comparison()
    demo_intensity_effects()
    demo_real_estate_showcase()
    
    print("\n🎉 演示完成！")
    print("💡 使用 'python animation_cli.py --list' 查看完整动画预设列表")
    print("📖 查看 'docs/animation-optimization-guide.md' 获取详细指南")

if __name__ == "__main__":
    main()
