#!/usr/bin/env python3
"""
🧪 快速测试脚本
Quick Test Script for Animation and Audio Fixes

按照规范要求的快速验证脚本：
1. 使用draft质量配置，最快速度生成
2. 测试文件统一放在tests文件夹
3. 只使用一个测试配置文件
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_header(title):
    """打印测试标题"""
    print("\n" + "="*50)
    print(f"🧪 {title}")
    print("="*50)

def run_test():
    """运行快速测试"""
    print_header("快速动画和音频测试")
    
    # 确保在正确的目录
    if not os.path.exists("animation_library.py"):
        print("❌ 请在项目根目录运行此脚本")
        return False
    
    # 清理之前的测试文件
    test_output = "tests/outputs/quick_test.mp4"
    if os.path.exists(test_output):
        os.remove(test_output)
        print(f"🧹 清理旧测试文件: {test_output}")
    
    # 确保输出目录存在
    os.makedirs("tests/outputs", exist_ok=True)
    
    print("\n🚀 开始快速测试...")
    print("📋 测试配置: tests/configs/quick_test.json")
    print("🎯 质量设置: draft (最快速度)")
    print("📁 输出目录: tests/outputs/")
    
    # 运行测试
    start_time = time.time()
    
    cmd = "python3 main.py generate tests/configs/quick_test.json -o quick_test.mp4"
    print(f"\n🔧 执行命令: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=180)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ 测试成功完成 (耗时: {end_time - start_time:.1f}秒)")
            
            # 检查输出文件
            if os.path.exists("output/quick_test.mp4"):
                file_size = os.path.getsize("output/quick_test.mp4") / (1024 * 1024)
                print(f"📁 输出文件: output/quick_test.mp4 ({file_size:.1f} MB)")
                
                # 移动到测试输出目录
                os.rename("output/quick_test.mp4", test_output)
                print(f"📦 文件已移动到: {test_output}")
                
                # 检查音频轨道
                check_audio(test_output)
                
                return True
            else:
                print("❌ 输出文件未生成")
                return False
        else:
            print(f"❌ 测试失败")
            print(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时 (3分钟)")
        return False
    except Exception as e:
        print(f"💥 测试异常: {e}")
        return False

def check_audio(video_file):
    """检查视频音频轨道"""
    print(f"\n🎵 检查音频轨道...")
    
    try:
        cmd = f"ffprobe -v quiet -select_streams a -show_entries stream=codec_name -of csv=p=0 {video_file}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            print(f"✅ 音频轨道正常: {result.stdout.strip()}")
        else:
            print("❌ 未检测到音频轨道")
    except:
        print("⚠️  无法检查音频轨道 (ffprobe未安装)")

def test_animation_library():
    """测试动画库功能"""
    print_header("动画库功能测试")
    
    try:
        # 测试动画库导入
        cmd = 'python3 -c "import animation_library; print(\\"✅ 动画库导入成功\\")"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 动画库导入成功")
        else:
            print("❌ 动画库导入失败")
            return False
        
        # 测试增强渲染器导入
        cmd = 'python3 -c "import enhanced_animation_renderer; print(\\"✅ 增强渲染器导入成功\\")"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 增强渲染器导入成功")
        else:
            print("❌ 增强渲染器导入失败")
            return False
        
        # 测试动画预设
        cmd = '''python3 -c "
from animation_library import animation_library
try:
    config = animation_library.get_animation('ken_burns', 1.0)
    print(f'✅ Ken Burns预设获取成功: {config.name}')
    
    presets = animation_library.get_real_estate_presets()
    print(f'✅ 房地产预设: {len(presets)}个')
    
except Exception as e:
    print(f'❌ 动画预设测试失败: {e}')
"'''
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        print(result.stdout.strip())
        
        return True
        
    except Exception as e:
        print(f"💥 动画库测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🎬 Reavo Video Creator - 快速测试")
    print("按照规范要求进行快速验证测试")
    
    # 测试1: 动画库功能
    if not test_animation_library():
        print("\n❌ 动画库测试失败，停止测试")
        return 1
    
    # 测试2: 视频生成
    if not run_test():
        print("\n❌ 视频生成测试失败")
        return 1
    
    # 测试完成
    print_header("测试完成")
    print("✅ 所有测试通过！")
    print("\n📊 测试总结:")
    print("  • 动画库功能: ✅ 正常")
    print("  • 增强渲染器: ✅ 正常")
    print("  • 视频生成: ✅ 正常")
    print("  • 音频处理: ✅ 正常")
    print("  • 动画效果: ✅ 正常")
    
    print(f"\n📁 测试文件位置:")
    print(f"  • 配置文件: tests/configs/quick_test.json")
    print(f"  • 输出视频: tests/outputs/quick_test.mp4")
    
    print("\n💡 提示:")
    print("  • 使用draft质量确保最快生成速度")
    print("  • 测试文件统一管理在tests文件夹")
    print("  • 可以播放输出视频验证效果")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
