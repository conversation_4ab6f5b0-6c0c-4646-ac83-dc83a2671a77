{"basic": {"output": {"filename": "Quick_Test_Animation_Audio", "quality": "720p"}, "audio": {"background": {"music": "https://storage.googleapis.com/pub_content/audios/Cabo_Cantina.mp3", "volume": 0.3}, "narration": {"volume": 1.0}}}, "intro": {"duration": 2.0, "title": {"text": "Quick Test", "style": {"font_size": 72, "color": "#FFD700", "position": "center"}}, "background": {"type": "color", "value": "#000000"}}, "segments": [{"id": "test-segment-1", "title": "Animation Test 1", "scenes": [{"id": "scene-1", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/14b297fd-ffd2-4740-9dbc-1bb8fa2deb16.mp3", "duration": 3.47}, "caption": {"text": "Testing <PERSON> animation effect.", "style": {"font_size": 36, "color": "white"}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/a5fd85e5-b5f0-4842-9824-26d1bc803caf.jpg", "animation": {"preset": "ken_burns", "intensity": 1.0, "scene_type": "exterior"}}}, {"id": "scene-2", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/6d82d05f-8776-4310-b1fe-3acfc46f4751.mp3", "duration": 3.0}, "caption": {"text": "Testing luxury reveal animation effect.", "style": {"font_size": 36, "color": "white"}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/4ac2ec9f-4868-4d33-9c03-fa757fc215bf.jpg", "animation": {"preset": "luxury_reveal", "intensity": 0.8, "scene_type": "interior"}}}]}, {"id": "test-segment-2", "title": "Animation Test 2", "scenes": [{"id": "scene-3", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/989bc332-2eb7-4059-ba1d-5956a077c65d.mp3", "duration": 3.0}, "caption": {"text": "Testing architectural focus animation.", "style": {"font_size": 36, "color": "white"}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/5f6a5a64-9340-464f-a3fd-077b70fbbc78.jpg", "animation": {"preset": "architectural_focus", "intensity": 1.0, "scene_type": "living_room"}}}, {"id": "scene-4", "audio": {"url": "https://storage.googleapis.com/media_output/2/ddd691ba-2c62-4c0e-b5a4-3497e1e8b3cd/7c40f1a0-e731-4319-9d8b-e2f8b066216e.mp3", "duration": 3.0}, "caption": {"text": "Testing dramatic zoom animation effect.", "style": {"font_size": 36, "color": "white"}}, "background": {"type": "image", "url": "https://storage.googleapis.com/media_input/2/43faf7ac-dd7e-4ad2-a94d-4362ba9d9868/0dac4e87-d737-425c-82f0-b0a432099a59.jpg", "animation": {"preset": "dramatic_zoom", "intensity": 0.8, "scene_type": "garden"}}}]}], "outro": {"duration": 2.0, "title": {"text": "Test Complete", "style": {"font_size": 48, "color": "#FFFFFF", "position": "center"}}, "background": {"type": "color", "value": "#000000"}}}