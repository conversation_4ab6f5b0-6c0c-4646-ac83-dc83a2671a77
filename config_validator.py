#!/usr/bin/env python3
"""
Reavo Video Creator - 配置验证器
支持新的四层配置结构验证：basic, intro, segments, outro
"""

import re
import uuid
from typing import Dict, Any, List, Optional, Tuple
from urllib.parse import urlparse
from pathlib import Path

class ValidationError(Exception):
    """配置验证错误"""
    def __init__(self, message: str, field_path: str = None):
        self.message = message
        self.field_path = field_path
        super().__init__(f"配置验证失败 [{field_path}]: {message}")

class ConfigValidator:
    """配置验证器 - 验证四层配置结构"""
    
    def __init__(self):
        """初始化配置验证器"""
        # 支持的配置字段
        self.supported_qualities = [
            # 推荐的新格式
            "standard", "high", "ultra",
            # 兼容的旧格式
            "720p", "1080p", "4k"
        ]
        self.supported_background_types = ["image", "video", "color"]
        self.supported_overlay_types = ["text", "image", "video"]
        self.supported_positions = [
            "top_left", "top_center", "top_right",
            "center_left", "center", "center_right", 
            "bottom_left", "bottom_center", "bottom_right"
        ]
        self.supported_animations = [
            "fade_in", "fade_out", "slide_up", "slide_down", 
            "slide_left", "slide_right", "zoom_in", "zoom_out"
        ]
        
        print("🔍 配置验证器初始化完成")
    
    def validate_config(self, config: Dict[str, Any]) -> List[str]:
        """
        验证完整配置
        
        Args:
            config: 配置字典
            
        Returns:
            错误列表，空列表表示验证通过
        """
        errors = []
        
        try:
            print("🔍 开始配置验证...")
            
            # 验证顶层结构
            errors.extend(self._validate_top_level_structure(config))
            
            # 验证basic配置
            if "basic" in config:
                errors.extend(self._validate_basic_config(config["basic"]))
            else:
                errors.append("缺少必需的 'basic' 配置")
            
            # 验证intro配置
            if "intro" in config:
                errors.extend(self._validate_intro_outro_config(config["intro"], "intro"))
            
            # 验证segments配置
            if "segments" in config:
                errors.extend(self._validate_segments_config(config["segments"]))
            
            # 验证outro配置
            if "outro" in config:
                errors.extend(self._validate_intro_outro_config(config["outro"], "outro"))
            
            if not errors:
                print("✅ 配置验证通过")
            else:
                print(f"❌ 发现 {len(errors)} 个配置错误")
                
        except Exception as e:
            errors.append(f"验证过程出错: {str(e)}")
        
        return errors
    
    def _validate_top_level_structure(self, config: Dict[str, Any]) -> List[str]:
        """验证顶层结构"""
        errors = []
        
        # 检查必需的顶层字段
        required_fields = ["basic"]
        for field in required_fields:
            if field not in config:
                errors.append(f"缺少必需的顶层字段: {field}")
        
        # 检查可选的顶层字段
        allowed_fields = ["basic", "intro", "segments", "outro"]
        for field in config.keys():
            if field not in allowed_fields:
                errors.append(f"不支持的顶层字段: {field}")
        
        return errors
    
    def _validate_basic_config(self, basic: Dict[str, Any]) -> List[str]:
        """验证basic配置"""
        errors = []
        
        # 验证output配置
        if "output" not in basic:
            errors.append("basic.output: 缺少必需的output配置")
        else:
            errors.extend(self._validate_output_config(basic["output"]))
        
        # 验证audio配置
        if "audio" not in basic:
            errors.append("basic.audio: 缺少必需的audio配置")
        else:
            errors.extend(self._validate_audio_config(basic["audio"]))
        
        return errors
    
    def _validate_output_config(self, output: Dict[str, Any]) -> List[str]:
        """验证output配置"""
        errors = []
        
        # 验证filename
        if "filename" not in output:
            errors.append("basic.output.filename: 缺少必需的filename字段")
        elif not isinstance(output["filename"], str) or not output["filename"].strip():
            errors.append("basic.output.filename: filename必须是非空字符串")
        
        # 验证quality
        if "quality" not in output:
            errors.append("basic.output.quality: 缺少必需的quality字段")
        elif output["quality"] not in self.supported_qualities:
            errors.append(f"basic.output.quality: 不支持的质量设置 '{output['quality']}'，支持: {self.supported_qualities}")
        
        # 验证resolution
        if "resolution" in output:
            resolution = output["resolution"]
            if not isinstance(resolution, list) or len(resolution) != 2:
                errors.append("basic.output.resolution: resolution必须是包含两个数字的数组 [width, height]")
            elif not all(isinstance(x, int) and x > 0 for x in resolution):
                errors.append("basic.output.resolution: 分辨率必须是正整数")
        
        return errors
    
    def _validate_audio_config(self, audio: Dict[str, Any]) -> List[str]:
        """验证audio配置"""
        errors = []
        
        # 验证各个音频组
        audio_groups = ["background", "narration", "music", "effects"]
        for group in audio_groups:
            if group in audio:
                errors.extend(self._validate_audio_group(audio[group], f"basic.audio.{group}"))
        
        return errors
    
    def _validate_audio_group(self, group: Dict[str, Any], path: str) -> List[str]:
        """验证音频组配置"""
        errors = []
        
        if not isinstance(group, dict):
            errors.append(f"{path}: 必须是对象类型")
            return errors
        
        # 验证音量字段
        for key, value in group.items():
            if key == "volume" and value is not None:
                if not isinstance(value, (int, float)) or value < 0 or value > 2:
                    errors.append(f"{path}.{key}: 音量必须是0-2之间的数字")
            elif key == "music" and value is not None:
                errors.extend(self._validate_url(value, f"{path}.{key}"))
        
        return errors
    
    def _validate_intro_outro_config(self, config: Dict[str, Any], section: str) -> List[str]:
        """验证intro/outro配置"""
        errors = []
        
        # 验证duration
        if "duration" not in config:
            errors.append(f"{section}.duration: 缺少必需的duration字段")
        elif not isinstance(config["duration"], (int, float)) or config["duration"] <= 0:
            errors.append(f"{section}.duration: duration必须是正数")
        
        # 验证title
        if "title" in config:
            errors.extend(self._validate_text_config(config["title"], f"{section}.title"))
        
        # 验证subtitle
        if "subtitle" in config:
            errors.extend(self._validate_text_config(config["subtitle"], f"{section}.subtitle"))
        
        # 验证background
        if "background" in config:
            errors.extend(self._validate_background_config(config["background"], f"{section}.background"))
        
        # 验证animation
        if "animation" in config:
            errors.extend(self._validate_animation_config(config["animation"], f"{section}.animation"))
        
        return errors
    
    def _validate_text_config(self, text_config: Dict[str, Any], path: str) -> List[str]:
        """验证文本配置"""
        errors = []
        
        if not isinstance(text_config, dict):
            errors.append(f"{path}: 必须是对象类型")
            return errors
        
        # 验证text字段
        if "text" not in text_config:
            errors.append(f"{path}.text: 缺少必需的text字段")
        elif not isinstance(text_config["text"], str):
            errors.append(f"{path}.text: text必须是字符串")
        
        # 验证style字段
        if "style" in text_config:
            errors.extend(self._validate_style_config(text_config["style"], f"{path}.style"))
        
        return errors
    
    def _validate_style_config(self, style: Dict[str, Any], path: str) -> List[str]:
        """验证样式配置"""
        errors = []
        
        if not isinstance(style, dict):
            errors.append(f"{path}: 必须是对象类型")
            return errors
        
        # 验证font_size
        if "font_size" in style:
            if not isinstance(style["font_size"], int) or style["font_size"] <= 0:
                errors.append(f"{path}.font_size: 字体大小必须是正整数")
        
        # 验证color
        if "color" in style:
            if not self._is_valid_color(style["color"]):
                errors.append(f"{path}.color: 无效的颜色格式，支持 #RRGGBB 或 #RGB")
        
        return errors
    
    def _validate_segments_config(self, segments: List[Dict[str, Any]]) -> List[str]:
        """验证segments配置"""
        errors = []
        
        if not isinstance(segments, list):
            errors.append("segments: 必须是数组类型")
            return errors
        
        if not segments:
            errors.append("segments: 至少需要一个segment")
            return errors
        
        used_ids = set()
        for i, segment in enumerate(segments):
            segment_path = f"segments[{i}]"
            
            # 验证segment结构
            errors.extend(self._validate_segment_config(segment, segment_path))
            
            # 检查ID重复
            if "id" in segment:
                segment_id = segment["id"]
                if segment_id in used_ids:
                    errors.append(f"{segment_path}.id: segment ID重复: {segment_id}")
                used_ids.add(segment_id)
        
        return errors
    
    def _validate_segment_config(self, segment: Dict[str, Any], path: str) -> List[str]:
        """验证单个segment配置"""
        errors = []
        
        # 验证必需字段
        required_fields = ["id", "title", "scenes"]
        for field in required_fields:
            if field not in segment:
                errors.append(f"{path}.{field}: 缺少必需字段")
        
        # 验证id格式
        if "id" in segment:
            if not self._is_valid_uuid(segment["id"]):
                errors.append(f"{path}.id: ID必须包含字母和数字，长度至少3个字符")
        
        # 验证title
        if "title" in segment:
            if not isinstance(segment["title"], str) or not segment["title"].strip():
                errors.append(f"{path}.title: title必须是非空字符串")
        
        # 验证scenes
        if "scenes" in segment:
            if not isinstance(segment["scenes"], list) or not segment["scenes"]:
                errors.append(f"{path}.scenes: scenes必须是非空数组")
            else:
                errors.extend(self._validate_scenes_config(segment["scenes"], f"{path}.scenes"))
        
        return errors
    
    def _validate_scenes_config(self, scenes: List[Dict[str, Any]], path: str) -> List[str]:
        """验证scenes配置"""
        errors = []
        
        used_ids = set()
        for i, scene in enumerate(scenes):
            scene_path = f"{path}[{i}]"
            
            # 验证scene结构
            errors.extend(self._validate_scene_config(scene, scene_path))
            
            # 检查ID重复
            if "id" in scene:
                scene_id = scene["id"]
                if scene_id in used_ids:
                    errors.append(f"{scene_path}.id: scene ID重复: {scene_id}")
                used_ids.add(scene_id)
        
        return errors
    
    def _validate_scene_config(self, scene: Dict[str, Any], path: str) -> List[str]:
        """验证单个scene配置"""
        errors = []
        
        # 验证必需字段 - 使用caption字段
        required_fields = ["id", "audio", "background"]
        for field in required_fields:
            if field not in scene:
                errors.append(f"{path}.{field}: 缺少必需字段")
        
        # 验证字幕字段 - 只支持caption字段
        if "caption" not in scene:
            errors.append(f"{path}.caption: 缺少必需的字幕字段")
        
        # 验证id
        if "id" in scene:
            if not self._is_valid_uuid(scene["id"]):
                errors.append(f"{path}.id: ID必须包含字母和数字，长度至少3个字符")
        
        # 验证audio
        if "audio" in scene:
            errors.extend(self._validate_scene_audio_config(scene["audio"], f"{path}.audio"))
        
        # 验证caption字幕配置
        if "caption" in scene:
            errors.extend(self._validate_scene_subtitle_config(scene["caption"], f"{path}.caption"))
        
        # 验证background
        if "background" in scene:
            errors.extend(self._validate_background_config(scene["background"], f"{path}.background"))
        
        # 验证可选字段
        if "avatar" in scene:
            errors.extend(self._validate_avatar_config(scene["avatar"], f"{path}.avatar"))
        
        if "overlays" in scene:
            errors.extend(self._validate_overlays_config(scene["overlays"], f"{path}.overlays"))
        
        return errors
    
    def _validate_scene_audio_config(self, audio: Dict[str, Any], path: str) -> List[str]:
        """验证场景音频配置"""
        errors = []
        
        # 验证url
        if "url" not in audio:
            errors.append(f"{path}.url: 缺少必需的url字段")
        else:
            errors.extend(self._validate_url(audio["url"], f"{path}.url"))
        
        # 验证duration
        if "duration" not in audio:
            errors.append(f"{path}.duration: 缺少必需的duration字段")
        elif not isinstance(audio["duration"], (int, float)) or audio["duration"] <= 0:
            errors.append(f"{path}.duration: duration必须是正数")
        
        return errors
    
    def _validate_scene_subtitle_config(self, subtitle: Dict[str, Any], path: str) -> List[str]:
        """验证场景字幕配置 - 支持新的样式和动画配置"""
        errors = []
        
        # 验证text字段
        if "text" not in subtitle:
            errors.append(f"{path}.text: 缺少必需的text字段")
        elif not isinstance(subtitle["text"], str):
            errors.append(f"{path}.text: text必须是字符串")
        
        # 验证可选的style配置
        if "style" in subtitle:
            errors.extend(self._validate_subtitle_style_config(subtitle["style"], f"{path}.style"))
        
        # 验证可选的animation配置
        if "animation" in subtitle:
            errors.extend(self._validate_subtitle_animation_config(subtitle["animation"], f"{path}.animation"))
        
        # 验证可选的位置配置
        if "position" in subtitle:
            position = subtitle["position"]
            if position not in self.supported_positions and position != "auto":
                errors.append(f"{path}.position: 不支持的位置 '{position}'，支持: {self.supported_positions} 或 'auto'")
        
        return errors
    
    def _validate_subtitle_style_config(self, style: Dict[str, Any], path: str) -> List[str]:
        """验证字幕样式配置"""
        errors = []
        
        if not isinstance(style, dict):
            errors.append(f"{path}: 必须是对象类型")
            return errors
        
        # 验证字体大小
        if "font_size" in style:
            font_size = style["font_size"]
            if not isinstance(font_size, int) or font_size <= 0:
                errors.append(f"{path}.font_size: 字体大小必须是正整数")
        
        # 验证颜色
        color_fields = ["color", "stroke_color", "background_color"]
        for field in color_fields:
            if field in style and style[field] is not None:
                if not self._is_valid_color(style[field]):
                    errors.append(f"{path}.{field}: 无效的颜色格式")
        
        # 验证描边宽度
        if "stroke_width" in style:
            stroke_width = style["stroke_width"]
            if not isinstance(stroke_width, int) or stroke_width < 0:
                errors.append(f"{path}.stroke_width: 描边宽度必须是非负整数")
        
        return errors
    
    def _validate_subtitle_animation_config(self, animation: Dict[str, Any], path: str) -> List[str]:
        """验证字幕动画配置"""
        errors = []
        
        if not isinstance(animation, dict):
            errors.append(f"{path}: 必须是对象类型")
            return errors
        
        # 验证动画类型
        if "type" in animation:
            animation_type = animation["type"]
            if animation_type not in self.supported_animations:
                errors.append(f"{path}.type: 不支持的动画类型 '{animation_type}'，支持: {self.supported_animations}")
        
        # 验证时长
        duration_fields = ["fade_in_duration", "fade_out_duration"]
        for field in duration_fields:
            if field in animation:
                duration = animation[field]
                if not isinstance(duration, (int, float)) or duration < 0:
                    errors.append(f"{path}.{field}: 时长必须是非负数")
        
        return errors
    
    def _validate_background_config(self, background: Dict[str, Any], path: str) -> List[str]:
        """验证背景配置"""
        errors = []
        
        # 验证type
        if "type" not in background:
            errors.append(f"{path}.type: 缺少必需的type字段")
        elif background["type"] not in self.supported_background_types:
            errors.append(f"{path}.type: 不支持的背景类型 '{background['type']}'，支持: {self.supported_background_types}")
        
        # 验证URL或value
        bg_type = background.get("type")
        if bg_type in ["image", "video"]:
            if "url" not in background:
                errors.append(f"{path}.url: {bg_type}类型背景需要url字段")
            else:
                errors.extend(self._validate_url(background["url"], f"{path}.url"))
        elif bg_type == "color":
            if "value" not in background:
                errors.append(f"{path}.value: color类型背景需要value字段")
            elif not self._is_valid_color(background["value"]):
                errors.append(f"{path}.value: 无效的颜色格式")
        
        # 验证animation
        if "animation" in background:
            errors.extend(self._validate_animation_config(background["animation"], f"{path}.animation"))
        
        return errors
    
    def _validate_avatar_config(self, avatar: Dict[str, Any], path: str) -> List[str]:
        """验证头像配置"""
        errors = []
        
        # 验证url
        if "url" not in avatar:
            errors.append(f"{path}.url: 缺少必需的url字段")
        else:
            errors.extend(self._validate_url(avatar["url"], f"{path}.url"))
        
        return errors
    
    def _validate_overlays_config(self, overlays: List[Dict[str, Any]], path: str) -> List[str]:
        """验证叠加层配置"""
        errors = []
        
        if not isinstance(overlays, list):
            errors.append(f"{path}: 必须是数组类型")
            return errors
        
        used_ids = set()
        for i, overlay in enumerate(overlays):
            overlay_path = f"{path}[{i}]"
            
            # 验证overlay结构
            errors.extend(self._validate_overlay_config(overlay, overlay_path))
            
            # 检查ID重复
            if "id" in overlay:
                overlay_id = overlay["id"]
                if overlay_id in used_ids:
                    errors.append(f"{overlay_path}.id: overlay ID重复: {overlay_id}")
                used_ids.add(overlay_id)
        
        return errors
    
    def _validate_overlay_config(self, overlay: Dict[str, Any], path: str) -> List[str]:
        """验证单个叠加层配置"""
        errors = []
        
        # 验证必需字段
        required_fields = ["id", "type", "content", "position"]
        for field in required_fields:
            if field not in overlay:
                errors.append(f"{path}.{field}: 缺少必需字段")
        
        # 验证id
        if "id" in overlay:
            if not self._is_valid_uuid(overlay["id"]):
                errors.append(f"{path}.id: ID必须包含字母和数字，长度至少3个字符")
        
        # 验证type
        if "type" in overlay:
            if overlay["type"] not in self.supported_overlay_types:
                errors.append(f"{path}.type: 不支持的叠加层类型 '{overlay['type']}'，支持: {self.supported_overlay_types}")
        
        # 验证position
        if "position" in overlay:
            if overlay["position"] not in self.supported_positions:
                errors.append(f"{path}.position: 不支持的位置 '{overlay['position']}'，支持: {self.supported_positions}")
        
        # 验证可选字段
        if "offset" in overlay:
            errors.extend(self._validate_offset_config(overlay["offset"], f"{path}.offset"))
        
        if "style" in overlay:
            errors.extend(self._validate_style_config(overlay["style"], f"{path}.style"))
        
        if "timing" in overlay:
            errors.extend(self._validate_timing_config(overlay["timing"], f"{path}.timing"))
        
        if "animation" in overlay:
            errors.extend(self._validate_animation_config(overlay["animation"], f"{path}.animation"))
        
        return errors
    
    def _validate_offset_config(self, offset: Dict[str, Any], path: str) -> List[str]:
        """验证偏移配置"""
        errors = []
        
        if not isinstance(offset, dict):
            errors.append(f"{path}: 必须是对象类型")
            return errors
        
        for coord in ["x", "y"]:
            if coord in offset:
                if not isinstance(offset[coord], int):
                    errors.append(f"{path}.{coord}: 必须是整数")
        
        return errors
    
    def _validate_timing_config(self, timing: Dict[str, Any], path: str) -> List[str]:
        """验证时序配置"""
        errors = []
        
        if not isinstance(timing, dict):
            errors.append(f"{path}: 必须是对象类型")
            return errors
        
        for field in ["start", "duration"]:
            if field in timing:
                if not isinstance(timing[field], (int, float)) or timing[field] < 0:
                    errors.append(f"{path}.{field}: 必须是非负数")
        
        return errors
    
    def _validate_animation_config(self, animation: Dict[str, Any], path: str) -> List[str]:
        """验证动画配置"""
        errors = []
        
        if not isinstance(animation, dict):
            errors.append(f"{path}: 必须是对象类型")
            return errors
        
        # 验证动画类型
        for key, value in animation.items():
            if key in ["entrance", "exit", "text_effect"] and value is not None:
                if value not in self.supported_animations:
                    errors.append(f"{path}.{key}: 不支持的动画效果 '{value}'，支持: {self.supported_animations}")
        
        return errors
    
    def _validate_url(self, url: str, path: str) -> List[str]:
        """验证URL格式"""
        errors = []
        
        if not isinstance(url, str):
            errors.append(f"{path}: URL必须是字符串")
            return errors
        
        if not url.strip():
            errors.append(f"{path}: URL不能为空")
            return errors
        
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                errors.append(f"{path}: 无效的URL格式")
        except Exception:
            errors.append(f"{path}: URL解析失败")
        
        return errors
    
    def _is_valid_color(self, color: str) -> bool:
        """检查颜色格式是否有效 - 支持十六进制格式和常见颜色名称"""
        if not isinstance(color, str):
            return False
        
        # 支持常见的颜色名称
        common_colors = {
            'white', 'black', 'red', 'green', 'blue', 'yellow', 'orange', 'purple', 
            'pink', 'brown', 'gray', 'grey', 'cyan', 'magenta', 'silver', 'gold',
            'transparent', 'none'
        }
        
        # 检查是否是常见颜色名称
        if color.lower() in common_colors:
            return True
        
        # 支持 #RGB 和 #RRGGBB 格式
        color_pattern = r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$'
        return bool(re.match(color_pattern, color))
    
    def _is_valid_uuid(self, uuid_string: str) -> bool:
        """检查ID格式是否有效 - 放宽限制，只要包含字母和数字即可"""
        if not isinstance(uuid_string, str):
            return False
        
        # 检查是否为空
        if not uuid_string.strip():
            return False
        
        # 放宽限制：只要包含字母和数字就可以，长度至少3个字符
        import re
        # 允许字母、数字、连字符、下划线
        pattern = r'^[a-zA-Z0-9_-]{3,}$'
        return bool(re.match(pattern, uuid_string))

# 导出主要类
__all__ = ['ConfigValidator', 'ValidationError'] 