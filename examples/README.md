# 📁 配置示例集合

本目录包含了各种使用场景的标准化配置示例，帮助用户快速上手 Reavo Video Creator。

## 📋 配置文件列表

### 📐 **configuration_showcase.json**
- **用途**: 统一配置系统完整展示
- **特点**: 演示V3四层架构的全部功能
- **质量**: `high` (高画质)
- **时长**: 约43秒
- **亮点**:
  - 配置架构详细说明
  - 质量预设系统展示
  - 多层视觉效果演示
  - 智能字幕系统展示
  - 完整的JSON注释说明

### 🚀 **quick_start.json**

- **用途**: 快速入门示例
- **特点**: 最简配置，适合初学者
- **质量**: `standard` (标准画质)
- **时长**: 约12秒
- **亮点**:
  - 简单的文字动画
  - 纯色背景
  - 基础字幕显示
  - 入门级配置结构

### 🎓 **educational_tutorial.json**

- **用途**: 教育教学视频
- **特点**: 课堂场景，学习氛围
- **质量**: `high` (高画质)
- **时长**: 约13秒
- **亮点**:
  - 真实课堂背景视频
  - 虚拟人物(Avatar)展示
  - 教学场景标签
  - 专业配音支持

### 🏠 **real_estate_showcase.json**

- **用途**: 房地产展示视频
- **特点**: 高端房产推广
- **质量**: `high` (高画质)
- **时长**: 约11秒
- **亮点**:
  - 高清房产图片
  - 地址标签overlay
  - 优雅的图片动画
  - 豪华房产展示风格

### 🎯 **marketing_video.json**

- **用途**: 营销推广视频
- **特点**: 商业产品宣传
- **质量**: `ultra` (超高画质)
- **时长**: 约32秒
- **亮点**:
  - 多样化视觉效果
  - 丰富的文字叠加
  - 行动号召(CTA)设计
  - 产品特性展示
  - 用户见证环节
  - 紧迫感营造

## 🎨 配置特点对比

| 配置文件             | 复杂度     | 场景类型 | 动画效果 | 适用人群     |
| -------------------- | ---------- | -------- | -------- | ------------ |
| configuration_showcase | ⭐⭐⭐⭐⭐ | 系统展示 | 全套特效 | 开发者/学习者 |
| quick_start          | ⭐         | 入门演示 | 基础淡入 | 初学者       |
| educational_tutorial | ⭐⭐⭐     | 教育培训 | 标签动画 | 教育工作者   |
| real_estate_showcase | ⭐⭐⭐     | 房产展示 | 图片缩放 | 房地产从业者 |
| marketing_video      | ⭐⭐⭐⭐⭐ | 营销推广 | 多重动画 | 营销专业人士 |

## 🛠️ 使用方法

### 1. 基础使用

```bash
# 查看质量预设说明
python main.py --quality

# 使用配置展示（了解全部功能）
python main.py examples/configuration_showcase.json

# 使用快速入门配置
python main.py examples/quick_start.json

# 使用教育教程配置
python main.py examples/educational_tutorial.json
```

### 2. 自定义修改

1. 复制任一配置文件
2. 根据需要修改内容
3. 更新音频/视频资源URL
4. 调整文字和样式
5. 运行生成视频

### 3. 质量选择建议

- **standard**: 快速预览，文件小
- **high**: 正式发布，平衡画质与体积
- **ultra**: 专业制作，最高画质
- **legacy_720p/1080p/4k**: 兼容旧版本

## 📖 配置结构说明

所有示例都遵循 V3 四层配置架构：

```
📦 配置结构
├── 🔧 basic: 基础设置（输出、音频、质量）
├── 🎬 intro: 开场片段（标题、背景、动画）
├── 📝 segments: 主要内容段落
│   └── 🎥 scenes: 具体场景
│       ├── 🎵 audio: 音频配置
│       ├── 💬 subtitle: 字幕文本
│       ├── 🖼️ background: 背景媒体
│       ├── 👤 avatar: 虚拟人物
│       └── 📐 overlays: 叠加元素
└── 🎭 outro: 结束片段（感谢、联系信息）
```

## 🔗 相关资源

- **用户指南**: `docs/guides/user-guide.md`
- **MoviePy API参考**: ../docs/references/MoviePy_API_Document.md
- **开发规范**: `docs/standards/development-guide.md`
- **项目文档**: `docs/README.md`

## 💡 提示

1. **URL替换**: 示例中的URL都是占位符，请替换为实际资源
2. **ID生成**: 使用UUID生成唯一的segment和scene ID
3. **时长匹配**: 确保音频时长与duration字段一致
4. **字幕优化**: 利用智能字幕系统自动换行
5. **资源管理**: 善用缓存系统提高效率

---

💡 **快速开始**: 从 `quick_start.json` 开始，逐步学习更复杂的配置！
 