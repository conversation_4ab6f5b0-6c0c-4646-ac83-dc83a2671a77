{"basic": {"output": {"filename": "Demo", "quality": "high"}, "audio": {"background": {"music": "https://storage.googleapis.com/pub_content/audios/Cabo_Cantina.mp3", "volume": 0.3}, "narration": {"volume": 1.0}, "music": {"intro": null, "outro": null, "volume": 0.8}, "effects": {"transition": null, "click": null, "success": null}}}, "intro": {"duration": 3, "title": {"text": "Python Learning Hub", "style": {"font_size": 72, "color": "#FFD700", "position": "center", "margin_top": -60}}, "subtitle": {"text": "Master Python Programming", "style": {"font_size": 48, "color": "#FFFFFF", "position": "center", "margin_top": 60}}, "background": {"type": "color", "value": "#000000"}, "animation": {"text_effect": "fade_in", "transition_out": "fade"}}, "segments": [{"id": "2c8f5a3b-e7d9-4f1a-8c2b-6e4d9f7a5c3e", "title": "Classroom", "scenes": [{"id": "a9b8c7d6-e5f4-3a2b-1c9d-8e7f6a5b4c3d", "audio": {"url": "https://storage.googleapis.com/media_output/2/47faa0af-4bbe-4b88-8768-39a096d5b2cf/b2faf3fb-722c-418e-a8dd-edcf73381ba2.mp3", "duration": 5.8}, "subtitle": {"text": "The classroom features students seated at individual desks, attentively facing the teacher at the front."}, "background": {"type": "video", "url": "https://storage.googleapis.com/media_input/2/47faa0af-4bbe-4b88-8768-39a096d5b2cf/91935f68-c33b-43f2-919a-23ceb6160296.mp4", "animation": {"zoom": 1.0, "brightness": 1.0, "contrast": 1.0, "saturation": 1.0, "blur": 0}}, "overlays": [{"id": "f1e2d3c4-b5a6-9f8e-7d6c-5b4a3e2f1d0c", "type": "text", "content": "Classroom", "position": "top_left", "offset": {"x": 50, "y": 50}, "style": {"font_size": 100, "color": "#FFFFFF", "background": "rgba(0,0,0,0.6)", "padding": 15, "border_radius": 8}, "timing": {"start": 0.5, "duration": 2.5}, "animation": {"entrance": "slide_down", "exit": "fade_out"}}], "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/classroom_student.mp4"}}, {"id": "b3c4d5e6-f7a8-9b0c-1d2e-3f4a5b6c7d8e", "audio": {"url": "https://storage.googleapis.com/media_output/2/47faa0af-4bbe-4b88-8768-39a096d5b2cf/94b038b8-1ea2-455f-870f-5e942047118d.mp3", "duration": 3.34}, "subtitle": {"text": "The room is brightly lit with natural light streaming in."}, "background": {"type": "video", "url": "https://storage.googleapis.com/media_input/2/47faa0af-4bbe-4b88-8768-39a096d5b2cf/91935f68-c33b-43f2-919a-23ceb6160296.mp4", "animation": {"zoom": 1.0, "pan": {"y": -20}}}, "avatar": {"url": "https://storage.googleapis.com/media_output/2/avatar_videos/classroom_teacher.mp4"}}]}], "outro": {"duration": 3, "title": {"text": "Thanks for watching!", "style": {"font_size": 72, "color": "#FFFFFF", "position": "center", "margin_top": -100}}, "subtitle": {"text": "YouTube: @PythonLearningHub", "style": {"font_size": 48, "color": "#CCCCCC", "position": "center", "margin_top": 50}}, "background": {"type": "color", "value": "#000000"}, "animation": {"text_effect": "slide_up", "transition_in": "fade"}}}