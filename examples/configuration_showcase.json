{"// 配置说明": "这是 Reavo Video Creator V3 统一配置系统的完整展示", "// 设计理念": "所有用户都使用相同的四层配置架构，通过质量预设和配置复杂度来满足不同需求", "// 质量预设": "standard(快速分享) | high(平衡画质) | ultra(专业制作)", "basic": {"// 基础设置层": "输出配置和音频配置", "output": {"filename": "Configuration_Showcase_Demo", "quality": "high", "// 质量说明": "使用high预设，自动配置1080p/30fps/4500k等参数"}, "audio": {"// 音频功能分组": "background(背景音乐) | narration(配音) | music(片头片尾音乐) | effects(音效)", "background": {"music": "https://example.com/cinematic_background.mp3", "volume": 0.25}, "narration": {"volume": 1.0}, "music": {"intro": "https://example.com/epic_intro.mp3", "outro": "https://example.com/inspiring_outro.mp3", "volume": 0.9}, "effects": {"transition": "whoosh", "click": "https://example.com/click_sound.wav", "success": "https://example.com/success_chime.wav"}}}, "intro": {"// 引导层": "开场片段，统一animation命名", "duration": 4.0, "title": {"text": "Reavo Video Creator", "style": {"font_size": 96, "color": "#FF6B35", "position": "center", "margin_top": -100}}, "subtitle": {"text": "统一配置系统 • 质量预设驱动 • 专业视频制作", "style": {"font_size": 36, "color": "#FFFFFF", "position": "center", "margin_top": 80}}, "background": {"type": "video", "url": "https://example.com/tech_showcase_bg.mp4"}, "animation": {"text_effect": "scale_in", "transition_out": "slide"}}, "segments": [{"// 内容层": "场景驱动架构，支持多层视觉效果", "id": "550e8400-e29b-41d4-a716-446655440000", "title": "配置架构展示", "scenes": [{"// 场景1": "展示基础四层配置", "id": "550e8400-e29b-41d4-a716-446655440001", "audio": {"url": "https://example.com/architecture_explanation.mp3", "duration": 10.0}, "subtitle": {"text": "V3统一配置架构包含四个层次：基础设置、引导片段、内容段落、结束片段。每个层次都有明确的职责和丰富的配置选项。"}, "background": {"type": "image", "url": "https://example.com/architecture_diagram.png", "animation": {"zoom": 1.05, "brightness": 1.1, "contrast": 1.05}}, "avatar": {"url": "https://example.com/tech_presenter.mp4"}, "overlays": [{"id": "architecture-layers", "type": "text", "content": "📦 Basic\n🎬 Intro\n📝 Segments\n🎭 Outro", "position": "center_left", "offset": {"x": 100, "y": 0}, "style": {"font_size": 48, "color": "#00FF88", "background": "rgba(0,0,0,0.8)", "padding": 30, "border_radius": 15}, "timing": {"start": 2.0, "duration": 6.0}, "animation": {"entrance": "slide_right", "exit": "fade_out"}}]}, {"// 场景2": "展示质量预设系统", "id": "550e8400-e29b-41d4-a716-446655440002", "audio": {"url": "https://example.com/quality_presets.mp3", "duration": 8.0}, "subtitle": {"text": "质量预设系统支持不同用途：标准质量适合快速分享，高清质量平衡画质与体积，超高清质量用于专业制作。"}, "background": {"type": "video", "url": "https://example.com/quality_comparison.mp4", "animation": {"zoom": 1.02, "pan": {"x": 0, "y": -15}}}, "overlays": [{"id": "quality-presets", "type": "text", "content": "📱 Standard (720p)\n🖥️ High (1080p)\n🎬 Ultra (4K)", "position": "top_right", "offset": {"x": -100, "y": 100}, "style": {"font_size": 32, "color": "#FFD700", "background": "rgba(0,0,0,0.9)", "padding": 20, "border_radius": 10}, "timing": {"start": 1.0, "duration": 6.0}, "animation": {"entrance": "slide_down", "exit": "slide_up"}}]}]}, {"// 段落2": "展示多层视觉效果", "id": "550e8400-e29b-41d4-a716-446655440003", "title": "多层视觉效果", "scenes": [{"// 场景3": "展示完整的五层视觉效果", "id": "550e8400-e29b-41d4-a716-446655440004", "audio": {"url": "https://example.com/visual_layers.mp3", "duration": 12.0}, "subtitle": {"text": "系统支持五层视觉效果：音频层控制声音，字幕层显示文本，背景层提供视觉基础，头像层展示虚拟人物，叠加层添加额外元素。"}, "background": {"type": "video", "url": "https://example.com/layers_demo.mp4", "animation": {"zoom": 1.08, "brightness": 0.95, "contrast": 1.1, "saturation": 1.05}}, "avatar": {"url": "https://example.com/virtual_presenter.mp4"}, "overlays": [{"id": "layer-1", "type": "image", "content": "https://example.com/audio_icon.png", "position": "top_left", "offset": {"x": 50, "y": 50}, "timing": {"start": 1.0, "duration": 2.0}, "animation": {"entrance": "bounce_in", "exit": "fade_out"}}, {"id": "layer-2", "type": "image", "content": "https://example.com/subtitle_icon.png", "position": "top_left", "offset": {"x": 50, "y": 120}, "timing": {"start": 3.0, "duration": 2.0}, "animation": {"entrance": "bounce_in", "exit": "fade_out"}}, {"id": "layer-3", "type": "image", "content": "https://example.com/background_icon.png", "position": "top_left", "offset": {"x": 50, "y": 190}, "timing": {"start": 5.0, "duration": 2.0}, "animation": {"entrance": "bounce_in", "exit": "fade_out"}}, {"id": "layer-4", "type": "image", "content": "https://example.com/avatar_icon.png", "position": "top_left", "offset": {"x": 50, "y": 260}, "timing": {"start": 7.0, "duration": 2.0}, "animation": {"entrance": "bounce_in", "exit": "fade_out"}}, {"id": "layer-5", "type": "image", "content": "https://example.com/overlay_icon.png", "position": "top_left", "offset": {"x": 50, "y": 330}, "timing": {"start": 9.0, "duration": 2.0}, "animation": {"entrance": "bounce_in", "exit": "fade_out"}}, {"id": "complete-stack", "type": "text", "content": "🎵 Audio Layer\n💬 Subtitle Layer\n🖼️ Background Layer\n👤 Avatar Layer\n📐 Overlay Layer", "position": "center_right", "offset": {"x": -150, "y": 0}, "style": {"font_size": 24, "color": "#FFFFFF", "background": "rgba(0,0,0,0.8)", "padding": 20, "border_radius": 8}, "timing": {"start": 10.0, "duration": 2.0}, "animation": {"entrance": "scale_in", "exit": "scale_out"}}]}]}, {"// 段落3": "展示智能字幕系统", "id": "550e8400-e29b-41d4-a716-446655440005", "title": "智能字幕系统", "scenes": [{"// 场景4": "展示智能字幕的自动换行功能", "id": "550e8400-e29b-41d4-a716-446655440006", "audio": {"url": "https://example.com/subtitle_intelligence.mp3", "duration": 9.0}, "subtitle": {"text": "智能字幕系统支持自动换行、中英文混合显示、动态画布调整、智能断点选择等功能。系统会自动优化字幕显示效果，确保最佳的可读性。"}, "background": {"type": "color", "value": "#2C3E50"}, "overlays": [{"id": "subtitle-features", "type": "text", "content": "✅ 自动换行\n✅ 中英文混合\n✅ 动态画布\n✅ 智能断点\n✅ 最大3行限制\n✅ 优先标点断点", "position": "center", "style": {"font_size": 40, "color": "#00FF88", "background": "rgba(0,0,0,0.7)", "padding": 40, "border_radius": 20}, "timing": {"start": 2.0, "duration": 6.0}, "animation": {"entrance": "typewriter", "exit": "fade_out"}}]}]}], "outro": {"// 结尾层": "与intro保持一致的简洁结构", "duration": 5.0, "title": {"text": "统一配置 • 灵活创作", "style": {"font_size": 72, "color": "#FFFFFF", "position": "center", "margin_top": -120}}, "subtitle": {"text": "一套配置架构，满足所有需求\n从快速分享到专业制作\n\n🚀 立即开始您的视频创作之旅", "style": {"font_size": 28, "color": "#CCCCCC", "position": "center", "margin_top": 60}}, "background": {"type": "video", "url": "https://example.com/success_celebration.mp4"}, "animation": {"text_effect": "scale_in", "transition_in": "fade"}}}