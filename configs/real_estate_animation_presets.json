{"real_estate_presets": {"description": "专为房地产视频设计的动画预设，确保专业、优雅、不过度夸张", "themes": {"luxury_home": {"name": "豪华住宅", "description": "适用于高端豪华住宅，动画优雅稳重", "background_animations": [{"preset": "gentle_zoom_in", "zoom": 1.05, "duration_factor": 1.0, "easing": "ease_in_out"}, {"preset": "gentle_zoom_out", "zoom": 0.95, "duration_factor": 1.0, "easing": "ease_in_out"}, {"preset": "gentle_zoom_in", "zoom": 1.03, "duration_factor": 1.0, "easing": "ease_in_out"}], "text_animations": {"fade_in": 0.8, "fade_out": 0.8, "scale_start": 0.98, "scale_end": 1.0}}, "modern_apartment": {"name": "现代公寓", "description": "适用于现代风格公寓，动画简洁现代", "background_animations": [{"preset": "gentle_zoom_out", "zoom": 0.97, "duration_factor": 1.0, "easing": "ease_in_out"}, {"preset": "gentle_zoom_in", "zoom": 1.04, "duration_factor": 1.0, "easing": "ease_in_out"}, {"preset": "gentle_zoom_out", "zoom": 0.96, "duration_factor": 1.0, "easing": "ease_in_out"}], "text_animations": {"fade_in": 0.6, "fade_out": 0.6, "scale_start": 0.98, "scale_end": 1.0}}, "family_home": {"name": "家庭住宅", "description": "适用于家庭住宅，动画温馨自然", "background_animations": [{"preset": "gentle_zoom_in", "zoom": 1.03, "duration_factor": 1.0, "easing": "ease_in_out"}, {"preset": "gentle_zoom_out", "zoom": 0.98, "duration_factor": 1.0, "easing": "ease_in_out"}, {"preset": "gentle_zoom_in", "zoom": 1.02, "duration_factor": 1.0, "easing": "ease_in_out"}], "text_animations": {"fade_in": 0.7, "fade_out": 0.7, "scale_start": 0.98, "scale_end": 1.0}}, "commercial_property": {"name": "商业地产", "description": "适用于商业地产，动画专业稳重", "background_animations": [{"preset": "gentle_zoom_out", "zoom": 0.96, "duration_factor": 1.0, "easing": "ease_in_out"}, {"preset": "gentle_zoom_in", "zoom": 1.04, "duration_factor": 1.0, "easing": "ease_in_out"}, {"preset": "gentle_zoom_out", "zoom": 0.97, "duration_factor": 1.0, "easing": "ease_in_out"}], "text_animations": {"fade_in": 0.9, "fade_out": 0.9, "scale_start": 0.98, "scale_end": 1.0}}}, "animation_definitions": {"gentle_zoom_in": {"type": "zoom", "description": "轻柔的放大效果", "default_zoom": 1.03, "max_zoom": 1.05, "easing": "ease_in_out"}, "gentle_zoom_out": {"type": "zoom", "description": "轻柔的缩小效果", "default_zoom": 0.97, "min_zoom": 0.95, "easing": "ease_in_out"}, "subtle_pan_right": {"type": "pan", "description": "微妙的右移效果", "default_pan": {"x": 25, "y": 0}, "max_pan": {"x": 40, "y": 0}, "easing": "ease_in_out"}, "subtle_pan_left": {"type": "pan", "description": "微妙的左移效果", "default_pan": {"x": -25, "y": 0}, "max_pan": {"x": -40, "y": 0}, "easing": "ease_in_out"}, "smooth_pan_left": {"type": "pan", "description": "平滑的左移效果", "default_pan": {"x": -30, "y": 0}, "max_pan": {"x": -45, "y": 0}, "easing": "ease_in_out"}, "smooth_pan_right": {"type": "pan", "description": "平滑的右移效果", "default_pan": {"x": 30, "y": 0}, "max_pan": {"x": 45, "y": 0}, "easing": "ease_in_out"}, "subtle_pan_up": {"type": "pan", "description": "微妙的上移效果", "default_pan": {"x": 0, "y": -20}, "max_pan": {"x": 0, "y": -35}, "easing": "ease_in_out"}, "subtle_pan_down": {"type": "pan", "description": "微妙的下移效果", "default_pan": {"x": 0, "y": 20}, "max_pan": {"x": 0, "y": 35}, "easing": "ease_in_out"}}, "usage_guidelines": {"scene_distribution": {"description": "建议的场景动画分配策略", "rules": ["每个主题的动画按顺序循环使用", "避免连续使用相同类型的动画", "缩放和平移动画交替使用", "保持整体节奏的一致性"]}, "parameter_limits": {"zoom_range": [0.95, 1.05], "pan_range": {"x": [-20, 20], "y": [-15, 15]}, "fade_duration": [0.5, 1.0], "scale_range": [0.98, 1.02]}}}}