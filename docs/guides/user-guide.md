# 📚 Reavo Video Creator 使用指南

> 用户和开发者的完整使用指南 - 从入门到精通

## 📋 目录
- [🚀 快速开始](#-快速开始)
- [⚙️ 配置文件详解](#-配置文件详解)
- [🎨 实战案例](#-实战案例)
- [🎯 高级功能](#-高级功能)
- [🔧 质量预设](#-质量预设)
- [✨ 最佳实践](#-最佳实践)
- [❓ 常见问题](#-常见问题)
- [🛠️ 故障排除](#-故障排除)

---

## 🚀 快速开始

### 系统要求
- **Python**: 3.9 或更高版本
- **系统**: Windows、macOS、Linux
- **内存**: 建议 4GB 以上
- **存储**: 至少 1GB 可用空间

### 安装步骤

#### 1. 安装依赖
```bash
# 进入项目目录
cd reavo_video_creator

# 安装Python依赖
pip install -r requirements.txt
```

#### 2. 验证安装
```bash
# 检查版本
python main.py --version

# 运行测试
python main.py --help
```

### 30秒生成第一个视频

#### 创建配置文件
创建 `my_first_video.json`：
```json
{
  "basic": {
    "output": {
      "filename": "hello_world",
      "quality": "high"
  },
  "audio": {
      "background": {"volume": 0.3},
      "narration": {"volume": 1.0}
    }
  },
  "segments": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "title": "Hello World",
      "scenes": [
        {
          "id": "550e8400-e29b-41d4-a716-446655440001",
          "audio": {
            "url": "https://example.com/hello.mp3",
            "duration": 5.0
          },
          "subtitle": {
            "text": "欢迎使用 Reavo Video Creator！"
          },
          "background": {
            "type": "color",
            "value": "#1a1a1a"
  }
        }
      ]
    }
  ]
}
```

#### 生成视频
```bash
# 生成视频
python main.py generate my_first_video.json

# 查看输出
ls output/
```

---

## ⚙️ 配置文件详解

### V3 四层配置架构

Reavo Video Creator 采用四层配置架构，层次清晰，功能强大：

```
📄 配置文件
├── 🔧 basic      (基础配置)
├── 🎬 intro      (片头配置)
├── 📺 segments   (内容配置)
└── 🎭 outro      (片尾配置)
```

### 1. Basic 配置 (基础设置)

```json
{
  "basic": {
    "output": {
      "filename": "my_video",          // 输出文件名
      "quality": "high",               // 质量预设
      "resolution": [1920, 1080]       // 分辨率（可选）
    },
    "audio": {
      "background": {
        "music": "https://example.com/bg.mp3",
        "volume": 0.3                  // 背景音乐音量
      },
      "narration": {
        "volume": 1.0                  // 旁白音量
      },
      "music": {
        "intro": "https://example.com/intro.mp3",
        "outro": "https://example.com/outro.mp3",
        "volume": 0.8                  // 音乐音量
      },
      "effects": {
        "transition": "fade",          // 转场音效
        "click": "https://example.com/click.wav",
        "success": "https://example.com/success.wav"
      }
    }
  }
}
```

### 2. Intro 配置 (片头设置)

```json
{
  "intro": {
    "duration": 3.0,                   // 片头时长
    "title": {
      "text": "我的视频标题",
      "style": {
        "fontsize": 60,
        "color": "white",
        "font": "Arial-Bold"
      }
    },
    "subtitle": {
      "text": "副标题描述",
      "style": {
        "fontsize": 30,
        "color": "#cccccc"
      }
    },
    "background": {
      "type": "image",                 // image/video/color
      "url": "https://example.com/intro_bg.jpg"
    },
    "animation": {
      "text_effect": "fade_in",       // 文本动画
      "transition_in": "fade",        // 进入转场
      "transition_out": "fade"        // 退出转场
    }
  }
}
```

### 3. Segments 配置 (内容片段)

最强大的配置部分，支持多层视觉效果：

```json
{
  "segments": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "title": "第一个片段",
      "scenes": [
        {
          "id": "550e8400-e29b-41d4-a716-446655440001",
          
          // 🎵 音频层
          "audio": {
            "url": "https://example.com/audio.mp3",
            "duration": 10.0
          },
          
          // 📝 字幕层
          "subtitle": {
            "text": "这里是字幕内容，支持自动换行"
          },
          
          // 🖼️ 背景层 - 支持专业动画预设
          "background": {
            "type": "image",
            "url": "https://example.com/background.jpg",
            "animation": {
              "preset": "ken_burns",        // 专业动画预设
              "intensity": 1.2,             // 强度控制
              "scene_type": "exterior"      // 场景类型
            }
          },
          
          // 👤 头像层
          "avatar": {
            "url": "https://example.com/avatar.png"
          },
          
          // 🎨 叠加层
          "overlays": [
            {
              "id": "overlay-001",
              "type": "text",
              "content": "重点强调文本",
              "position": "center",
              "offset": {"x": 0, "y": 100},
        "style": {
                "fontsize": 40,
                "color": "yellow",
                "stroke_color": "black",
                "stroke_width": 2
              },
              "timing": {
                "start": 2.0,
                "duration": 3.0
              },
              "animation": {
                "entrance": "slide_up",
                "exit": "fade_out"
        }
      }
          ]
        }
      ]
    }
  ]
}
```

### 4. Outro 配置 (片尾设置)

与 intro 配置结构相同，用于片尾展示：

```json
{
  "outro": {
    "duration": 5.0,
    "title": {
      "text": "感谢观看",
      "style": {
        "fontsize": 50,
        "color": "white"
      }
    },
    "subtitle": {
      "text": "请点赞订阅支持",
      "style": {
        "fontsize": 25,
        "color": "#ffdd00"
      }
    },
    "background": {
      "type": "color",
      "value": "#000000"
    },
    "animation": {
      "text_effect": "typewriter",
      "transition_in": "fade",
      "transition_out": "fade"
    }
  }
}
```

---

## 🎨 实战案例

### 案例1：教育视频制作

创建一个Python编程教程视频：

```json
{
  "basic": {
    "output": {
      "filename": "Python_Tutorial_Basics",
      "quality": "high"
    },
    "audio": {
      "background": {
        "music": "https://example.com/study_music.mp3",
        "volume": 0.2
      }
    }
  },
  "intro": {
    "duration": 4.0,
      "title": {
      "text": "Python 编程入门",
        "style": {
        "fontsize": 65,
        "color": "white",
        "font": "Arial-Bold"
        }
      },
      "subtitle": {
      "text": "从零开始学习Python",
      "style": {
        "fontsize": 35,
        "color": "#4CAF50"
      }
    },
    "background": {
      "type": "color",
      "value": "#1e1e1e"
    }
  },
  "segments": [
    {
      "id": "lesson-001",
      "title": "变量和数据类型",
      "scenes": [
        {
          "id": "scene-001",
          "audio": {
            "url": "https://example.com/lesson1_audio.mp3",
            "duration": 25.0
          },
          "subtitle": {
            "text": "在Python中，变量不需要声明类型。我们可以直接给变量赋值。"
          },
          "background": {
            "type": "image",
            "url": "https://example.com/code_background.jpg"
          },
          "overlays": [
            {
              "id": "code-example",
              "type": "text",
              "content": "name = \"Alice\"\nage = 25\nheight = 5.6",
              "position": "center",
        "style": {
                "fontsize": 24,
                "color": "#00ff00",
                "font": "Consolas"
              },
              "timing": {
                "start": 5.0,
                "duration": 15.0
              }
            }
          ]
        }
      ]
    }
  ],
  "outro": {
    "duration": 3.0,
    "title": {
      "text": "下节课预告",
      "style": {
        "fontsize": 45,
        "color": "white"
      }
    },
    "subtitle": {
      "text": "函数和模块的使用",
      "style": {
        "fontsize": 30,
        "color": "#4CAF50"
      }
    }
  }
}
```

### 案例2：产品展示视频

```json
{
  "basic": {
    "output": {
      "filename": "Product_Showcase_2025",
      "quality": "ultra"
    },
    "audio": {
      "background": {
        "music": "https://example.com/corporate_music.mp3",
        "volume": 0.4
      }
    }
  },
  "intro": {
    "duration": 5.0,
    "title": {
      "text": "新品发布",
      "style": {
        "fontsize": 70,
        "color": "#ffffff",
        "stroke_color": "#000000",
        "stroke_width": 3
      }
    },
    "background": {
      "type": "video",
      "url": "https://example.com/product_intro.mp4"
    },
    "animation": {
      "text_effect": "scale_in",
      "transition_in": "fade",
      "transition_out": "slide"
    }
  },
  "segments": [
    {
      "id": "product-features",
      "title": "核心特性",
      "scenes": [
        {
          "id": "feature-1",
          "audio": {
            "url": "https://example.com/feature1_narration.mp3",
            "duration": 12.0
          },
          "subtitle": {
            "text": "革命性的设计理念，为用户带来前所未有的体验"
      },
          "background": {
            "type": "video",
            "url": "https://example.com/product_demo.mp4",
            "animation": {
              "type": "slow_zoom",
              "duration": 12.0
            }
          },
          "avatar": {
            "url": "https://example.com/presenter.png"
          },
          "overlays": [
            {
              "id": "feature-badge",
              "type": "image",
              "content": "https://example.com/new_badge.png",
              "position": "top_right",
              "offset": {"x": -50, "y": 50},
              "timing": {
                "start": 0.0,
                "duration": 12.0
              },
              "animation": {
                "entrance": "bounce_in"
        }
      }
          ]
        }
      ]
    }
  ]
}
```

---

## 🎯 高级功能

### 智能字幕系统

Reavo Video Creator 内置智能字幕系统，支持：

#### 自动换行
```json
{
  "subtitle": {
    "text": "这是一段很长的字幕文本，系统会自动根据屏幕宽度进行换行处理，确保显示效果美观"
  }
}
```

#### 中英文混合
```json
{
  "subtitle": {
    "text": "支持中文和English混合显示，智能断点选择"
  }
}
```

#### 样式定制
```json
{
  "subtitle": {
    "text": "自定义样式字幕",
    "style": {
      "fontsize": 40,
      "color": "white",
      "stroke_color": "black",
      "stroke_width": 2,
      "font": "Arial-Bold"
    }
  }
}
```

### 多层视觉效果

#### 位置系统
支持9种锚点定位：
```json
{
  "position": "top_left",     // 左上角
  "position": "top_center",   // 顶部居中
  "position": "top_right",    // 右上角
  "position": "center_left",  // 左侧居中
  "position": "center",       // 正中央
  "position": "center_right", // 右侧居中
  "position": "bottom_left",  // 左下角
  "position": "bottom_center",// 底部居中
  "position": "bottom_right"  // 右下角
}
```

#### 偏移调整
```json
{
  "position": "center",
  "offset": {
    "x": 100,    // 向右偏移100像素
    "y": -50     // 向上偏移50像素
  }
}
```

### 背景动画效果

#### 专业动画预设 (推荐)
使用专业动画预设，一键应用电影级背景效果：

```json
{
  "background": {
    "type": "image",
    "url": "property_image.jpg",
    "animation": {
      "preset": "ken_burns",        // 动画预设名称
      "intensity": 1.2,             // 强度控制 (0.1-2.0)
      "scene_type": "exterior"      // 场景类型优化
    }
  }
}
```

#### 可用动画预设

**🏠 房地产专业动画**
- `ken_burns`: 经典纪录片风格，适合建筑外观
- `luxury_reveal`: 豪华揭示效果，适合室内展示
- `parallax_up/down/left/right`: 视差滚动，创造空间深度感
- `architectural_focus`: 建筑焦点效果，突出设计细节

**🎬 电影级动画**
- `dramatic_zoom`: 戏剧性缩放，强烈视觉冲击
- `dolly_zoom`: 推拉镜头效果，希区柯克式变焦

#### 智能场景推荐
系统根据场景类型自动推荐最佳动画：
- `exterior` → `ken_burns` (建筑外观)
- `interior` → `parallax_up` (室内空间)
- `pool` → `parallax_down` (泳池水景)
- `bedroom` → `luxury_reveal` (卧室展示)

### 叠加层动画效果

#### 入场动画
```json
{
  "animation": {
    "entrance": "fade_in",      // 淡入
    "entrance": "slide_up",     // 上滑
    "entrance": "slide_down",   // 下滑
    "entrance": "slide_left",   // 左滑
    "entrance": "slide_right",  // 右滑
    "entrance": "scale_in",     // 缩放进入
    "entrance": "bounce_in"     // 弹跳进入
  }
}
```

#### 退场动画
```json
{
  "animation": {
    "exit": "fade_out",         // 淡出
    "exit": "slide_out_up",     // 上滑退出
    "exit": "scale_out",        // 缩放退出
    "exit": "bounce_out"        // 弹跳退出
  }
}
```

---

## 🔧 质量预设

### 推荐预设 (新格式)

#### standard - 标准质量
```json
{
  "basic": {
    "output": {
      "quality": "standard"
    }
  }
}
```
- **分辨率**: 1280x720
- **帧率**: 24 fps
- **码率**: 2000k
- **适用**: 社交媒体分享、快速预览、移动端观看
- **特点**: 文件小，处理快，1x文件大小，1x渲染时间

#### high - 高清质量
```json
{
  "basic": {
    "output": {
      "quality": "high"
    }
  }
}
```
- **分辨率**: 1920x1080
- **帧率**: 30 fps
- **码率**: 4500k
- **适用**: 网络发布、电脑观看、一般用途
- **特点**: 平衡画质与文件大小，2.5x文件大小，2x渲染时间

#### ultra - 超高清质量
```json
{
  "basic": {
    "output": {
      "quality": "ultra"
    }
  }
}
```
- **分辨率**: 3840x2160 (4K)
- **帧率**: 30 fps
- **码率**: 12000k
- **适用**: 专业制作、大屏播放、存档保存
- **特点**: 最佳画质，6x文件大小，4.5x渲染时间

### 兼容预设 (旧格式)

仍然支持但建议迁移：
- `720p` → 建议使用 `standard`
- `1080p` → 建议使用 `high`
- `4k` → 建议使用 `ultra`

### 自定义分辨率

```json
{
  "basic": {
    "output": {
      "quality": "high",
      "resolution": [1366, 768]  // 自定义分辨率
    }
  }
}
```

---

## ✨ 最佳实践

### 1. 配置组织

#### 模块化配置
将大型配置拆分为多个文件：
```bash
project/
├── config/
│   ├── base_config.json     # 基础配置
│   ├── intro_config.json    # 片头配置
│   └── segments/
│       ├── lesson1.json     # 第一课
│       └── lesson2.json     # 第二课
└── generate.py              # 生成脚本
```

#### 配置复用
```json
{
  "basic": {
    "output": {
      "filename": "lesson_{{lesson_number}}",
      "quality": "high"
    },
    "audio": {
      "background": {
        "music": "{{background_music_url}}",
        "volume": 0.3
      }
    }
  }
}
```

### 2. 资源管理

#### 网络资源
```json
{
  "background": {
    "type": "image",
    "url": "https://cdn.example.com/backgrounds/tech_bg.jpg"
  }
}
```

#### 本地资源
```json
{
  "background": {
    "type": "image",
    "url": "./assets/images/background.jpg"
  }
}
```

#### 资源缓存
系统自动缓存网络资源，提高后续生成速度。

### 3. 性能优化

#### 预览模式
```bash
# 快速预览（低质量）
python main.py preview config.json

# 完整渲染
python main.py generate config.json
```

#### 批量处理
```bash
# 批量生成多个视频
python main.py batch configs/
```

### 4. 内容规划

#### 时间安排
- **片头**: 2-5秒，简洁有力
- **内容**: 根据音频时长，通常10-30秒一个场景
- **片尾**: 3-8秒，包含总结或下期预告

#### 视觉层次
1. **背景层**: 提供视觉基础
2. **字幕层**: 主要信息传达
3. **头像层**: 增加人格化
4. **叠加层**: 强调重点内容

---

## ❓ 常见问题

### Q1: 如何设置自动换行？

**A**: 字幕系统自动处理换行，无需手动设置。系统会根据屏幕宽度和字体大小智能换行。

```json
{
  "subtitle": {
    "text": "这是一段很长的文本，系统会自动换行显示"
  }
}
```

### Q2: 如何调整视频质量？

**A**: 使用质量预设或自定义分辨率：

```json
{
  "basic": {
    "output": {
      "quality": "high",              // 使用预设
      "resolution": [1920, 1080]      // 或自定义
    }
  }
}
```

### Q3: 支持哪些音频格式？

**A**: 支持常见音频格式：
- **MP3**: 推荐，兼容性好
- **WAV**: 高质量，文件较大
- **AAC**: 现代格式，压缩率高
- **M4A**: 苹果格式，质量好

### Q4: 如何添加多个字幕？

**A**: 使用 overlays 叠加层：

```json
{
  "subtitle": {
    "text": "主字幕"
  },
  "overlays": [
    {
      "id": "sub-1",
      "type": "text",
      "content": "副字幕1",
      "position": "top_center"
    },
    {
      "id": "sub-2", 
      "type": "text",
      "content": "副字幕2",
      "position": "bottom_center"
    }
  ]
}
```

### Q5: 如何控制动画时间？

**A**: 使用 timing 配置：

```json
{
  "overlays": [
    {
      "id": "timed-text",
      "type": "text",
      "content": "3秒后出现，持续5秒",
      "timing": {
        "start": 3.0,        // 3秒后开始
        "duration": 5.0      // 持续5秒
      }
    }
  ]
}
```

### Q6: 如何预览视频效果？

**A**: 使用预览命令：

```bash
# 快速预览
python main.py preview config.json

# 指定预览时长
python main.py preview config.json --duration 10
```

---

## 🛠️ 故障排除

### 音频问题

#### 音频不同步
```json
{
  "audio": {
    "url": "audio.mp3",
    "duration": 10.0    // 明确指定时长
  }
}
```

#### 音量过大或过小
```json
{
  "audio": {
    "background": {
      "volume": 0.3     // 调整背景音乐音量
    },
    "narration": {
      "volume": 1.0     // 调整旁白音量
    }
  }
}
```

### 字幕问题

#### 字幕显示不全
- 检查字体是否存在
- 调整字幕大小
- 检查屏幕分辨率

#### 中文字符显示问题
```json
{
  "subtitle": {
    "text": "中文字幕",
    "style": {
      "font": "Arial Unicode MS"  // 使用支持中文的字体
    }
  }
}
```

### 性能问题

#### 生成速度慢
- 使用较低的质量预设
- 减少特效数量
- 使用本地资源而非网络资源

#### 内存不足
- 分段处理长视频
- 降低分辨率
- 关闭不必要的程序

### 文件问题

#### 资源文件404
```bash
# 检查资源URL
curl -I "https://example.com/audio.mp3"

# 使用本地资源
"url": "./assets/audio/background.mp3"
```

#### 输出文件损坏
- 检查磁盘空间
- 验证输入文件完整性
- 重新生成视频

---

## 🎓 进阶技巧

### 1. 使用变量和模板
```python
# 生成脚本示例
import json

template = {
    "basic": {
        "output": {
            "filename": f"lesson_{lesson_num}",
            "quality": "high"
        }
    }
}

# 批量生成配置
for i in range(1, 11):
    config = template.copy()
    config["basic"]["output"]["filename"] = f"lesson_{i:02d}"
    
    with open(f"config_lesson_{i:02d}.json", "w") as f:
        json.dump(config, f, indent=2)
```

### 2. 配置验证
```bash
# 验证配置文件
python main.py validate config.json

# 批量验证
python main.py validate configs/*.json
```

### 3. 性能监控
```bash
# 显示详细信息
python main.py generate config.json --verbose

# 性能分析
python main.py generate config.json --profile
```

---

**🎉 恭喜！您已经掌握了 Reavo Video Creator 的完整使用方法。现在开始创作您的专业视频吧！**

---

**📅 最后更新**: 2025-01-07  
**📊 指南版本**: v1.0  
**🎯 适用**: 所有 Reavo Video Creator 用户

---

**开始您的视频创作之旅！** 🎬✨ 