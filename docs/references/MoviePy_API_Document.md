# MoviePy 完整 API 参考手册

> 🎬 基于 MoviePy 2.x 官方文档整理的完整API参考
> 
> **重要**: 本文档基于官方文档编写，确保API使用的准确性！

## 📚 目录

- [Clip 基类](#clip-基类)
- [视频剪辑类](#视频剪辑类)
- [音频剪辑类](#音频剪辑类)
- [时间操作方法](#时间操作方法)
- [效果系统](#效果系统)
- [视频效果 (vfx)](#视频效果-vfx)
- [音频效果 (afx)](#音频效果-afx)
- [自定义滤镜](#自定义滤镜)
- [导入方式](#导入方式)
- [最佳实践](#最佳实践)

---

## Clip 基类

`moviepy.Clip.Clip` 是所有剪辑（VideoClips 和 AudioClips）的基类。

### 继承关系
```
Clip (基类)
├── VideoClip (视频剪辑)
│   ├── VideoFileClip (视频文件)
│   ├── ImageClip (图像剪辑)
│   ├── TextClip (文本剪辑)
│   ├── ColorClip (纯色剪辑)
│   ├── VideoClip (自定义视频)
│   ├── DataVideoClip (数据驱动视频)
│   └── UpdatedVideoClip (更新视频)
├── AudioClip (音频剪辑)
│   ├── AudioFileClip (音频文件)
│   └── CompositeAudioClip (音频合成)
└── CompositeVideoClip (视频合成)
```

### 基本属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `start` | `float` | 在组合中开始播放的时间（秒） |
| `end` | `float` | 在组合中停止播放的时间（秒） |
| `duration` | `float` | 剪辑的时长（秒），无限剪辑为 `None` |

### 基础方法

#### `close()`
释放正在使用的任何资源。**重要**：特别是在 Windows 上，务必调用此方法释放文件锁。

```python
clip.close()
```

#### `copy()`
创建剪辑的副本，支持链式调用。

```python
new_clip = clip.copy()
```

#### `get_frame(t)`
获取时间 t 处的帧数据。

**参数:**
- `t` (float | tuple | str): 时间点

```python
frame = clip.get_frame(10.5)  # 获取 10.5 秒处的帧
```

#### `iter_frames(fps=None, with_times=False, logger=None, dtype=None)`
迭代剪辑的所有帧。

**参数:**
- `fps` (int, optional): 迭代帧率
- `with_times` (bool, optional): 是否返回 (时间, 帧) 元组
- `logger` (str, optional): 进度显示方式 ("bar" 或 None)
- `dtype` (type, optional): NumPy 数组类型

```python
# 迭代所有帧
for frame in clip.iter_frames(fps=24):
    print(frame.shape)

# 带时间信息
for t, frame in clip.iter_frames(fps=24, with_times=True):
    print(f"时间: {t}, 帧大小: {frame.shape}")
```

#### `is_playing(t)`
检查时间 t 是否在剪辑播放范围内。

```python
if clip.is_playing(5.0):
    print("5秒时正在播放")
```

---

## 时间操作方法

### 剪辑操作

#### `subclipped(start_time=0, end_time=None)` ⭐
返回指定时间段的子剪辑。**最常用的方法之一！**

**参数:**
- `start_time` (float | tuple | str): 开始时间
- `end_time` (float | tuple | str): 结束时间

**时间格式支持:**
- 秒数：`10.5`
- 元组：`(1, 30)` 表示 1分30秒，`(1, 2, 30)` 表示 1小时2分30秒
- 字符串：`'01:02:30'` 或 `'00:01:30.50'`
- 负数：从结尾计算，`-10` 表示倒数第10秒

```python
# 提取前 10 秒
short_clip = clip.subclipped(0, 10)

# 提取 5-15 秒
middle_clip = clip.subclipped(5, 15)

# 去掉最后 2 秒
trimmed_clip = clip.subclipped(0, -2)

# 支持多种时间格式
clip.subclipped('00:01:30', '00:02:00')  # 1分30秒到2分钟
clip.subclipped((1, 30), (2, 0))         # 同上，使用元组格式
```

#### `with_section_cut_out(start_time, end_time)`
跳过指定时间段，返回去除中间部分的剪辑。

```python
# 去掉 10-20 秒的部分
edited_clip = clip.with_section_cut_out(10, 20)
```

### 时间设置

#### `with_duration(duration, change_end=True)`
设置剪辑时长。

**参数:**
- `duration` (float): 新的时长
- `change_end` (bool): 是否调整结束时间

```python
# 设置为 5 秒
clip_5s = clip.with_duration(5)
```

#### `with_start(t, change_end=True)`
设置开始时间（用于组合剪辑）。

**注意**: 这不会改变剪辑本身的内容，只是在组合中的开始时间。

```python
# 在组合中从第 3 秒开始播放
delayed_clip = clip.with_start(3)
```

#### `with_end(t)`
设置结束时间（用于组合剪辑）。

```python
# 在组合中到第 10 秒停止
ending_clip = clip.with_end(10)
```

### 速度控制

#### `with_speed_scaled(factor=None, final_duration=None)`
调整播放速度。

**参数:**
- `factor` (float): 速度倍数
- `final_duration` (float): 最终时长

```python
# 2 倍速播放
fast_clip = clip.with_speed_scaled(2.0)

# 慢动作（0.5 倍速）
slow_clip = clip.with_speed_scaled(0.5)

# 指定最终时长（自动计算速度倍数）
specific_duration_clip = clip.with_speed_scaled(final_duration=5.0)
```

#### `time_transform(time_func, apply_to=None, keep_duration=False)`
自定义时间变换。

**参数:**
- `time_func` (function): 时间变换函数 `t -> new_t`
- `apply_to` (list): 应用到 `["mask", "audio"]`
- `keep_duration` (bool): 是否保持时长

```python
# 倒放
reversed_clip = clip.time_transform(lambda t: clip.duration - t)

# 2倍速（等同于 with_speed_scaled(2)）
fast_clip = clip.time_transform(lambda t: 2*t, apply_to=['mask', 'audio'])

# 正弦波速度变化
oscillating_clip = clip.time_transform(
    lambda t: clip.duration * (1 + np.sin(2*np.pi*t/clip.duration))/2
)
```

---

## 视频剪辑类

### VideoFileClip
从视频文件加载剪辑。

```python
from moviepy import VideoFileClip

# 基本加载
clip = VideoFileClip("video.mp4")

# 可选参数
clip = VideoFileClip(
    "video.mp4",
    audio=True,        # 是否包含音频
    target_resolution=None,  # 目标分辨率
    resize_algorithm='bicubic'  # 缩放算法
)

# 自动释放资源
with VideoFileClip("video.mp4") as clip:
    # 处理视频
    processed = clip.subclipped(0, 10)
# 自动调用 close()
```

### ImageClip
从图像文件创建剪辑。

```python
from moviepy import ImageClip

# 从文件加载
clip = ImageClip("image.jpg")

# 从 numpy 数组
import numpy as np
image_array = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
clip = ImageClip(image_array)

# 设置时长（默认无限）
clip = ImageClip("image.jpg").with_duration(5)
```

### TextClip
创建文本剪辑。

```python
from moviepy import TextClip

# 基本文本
text = TextClip(text="Hello MoviePy!", font_size=50, color='white')

# 详细参数
text = TextClip(
    text="Hello MoviePy!",
    font_size=50,
    color='white',
    bg_color='black',
    font='Arial.ttf',  # 字体文件路径
    method='caption',  # 'label' 或 'caption'
    align='center',    # 文本对齐
    size=(640, 480)   # 画布大小
)

# 设置位置和时长
text = text.with_position('center').with_duration(5)
```

### ColorClip
创建纯色剪辑。

```python
from moviepy import ColorClip

# 红色背景
red_clip = ColorClip(size=(640, 480), color=(255, 0, 0), duration=5)

# RGB 或 HEX 颜色
blue_clip = ColorClip(size=(640, 480), color='blue', duration=3)
hex_clip = ColorClip(size=(640, 480), color='#FF5733', duration=3)
```

### VideoClip (自定义)
创建基于函数的自定义视频。

```python
from moviepy import VideoClip
import numpy as np

def make_frame(t):
    # 创建 480x640x3 的数组
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 根据时间 t 生成内容
    intensity = int(255 * (0.5 + 0.5 * np.sin(2 * np.pi * t)))
    frame[:, :, 0] = intensity  # 红色通道
    
    return frame

# 创建 5 秒的自定义视频
custom_clip = VideoClip(make_frame, duration=5)
```

---

## 音频剪辑类

### AudioFileClip
从音频文件加载剪辑。

```python
from moviepy import AudioFileClip

# 基本加载
audio = AudioFileClip("audio.wav")

# 自动释放资源
with AudioFileClip("audio.mp3") as audio:
    processed = audio.subclipped(0, 30)
```

### 音频操作

#### `with_volume_scaled(factor, start_time=None, end_time=None)`
调整音量。

```python
# 音量减半
quiet_clip = clip.with_volume_scaled(0.5)

# 只在 5-10 秒调整音量
partial_volume = clip.with_volume_scaled(0.3, start_time=5, end_time=10)
```

---

## 效果系统

### 应用效果

#### `with_effects(effects)` ⭐
应用效果列表。**推荐的效果应用方式！**

```python
from moviepy import vfx, afx

# 应用单个效果
clip_with_effect = clip.with_effects([vfx.Resize(0.5)])

# 应用多个效果
clip_with_effects = clip.with_effects([
    vfx.Resize(0.5),
    vfx.FadeIn(1),
    vfx.FadeOut(1)
])

# 音视频组合效果
mixed_effects = clip.with_effects([
    vfx.FadeIn(1),
    afx.AudioFadeIn(1)
])
```

### 通用变换

#### `transform(func, apply_to=None, keep_duration=True)`
通用帧变换函数。

**参数:**
- `func` (function): 变换函数 `(get_frame, t) -> frame`
- `apply_to` (list): 应用到 `["mask", "audio"]`
- `keep_duration` (bool): 是否保持时长

```python
# 自定义滤镜
def brightness_filter(get_frame, t):
    frame = get_frame(t)
    return frame * 1.3  # 增加亮度

bright_clip = clip.transform(brightness_filter)
```

#### `image_transform(func, apply_to=None)`
仅变换图像帧（仅适用于 VideoClip）。

```python
def invert_green_blue(frame):
    # 交换绿色和蓝色通道
    frame[:, :, [1, 2]] = frame[:, :, [2, 1]]
    return frame

modified_clip = video_clip.image_transform(invert_green_blue)
```

---

## 视频效果 (vfx)

### 导入方式
```python
from moviepy import vfx
```

### 常用视频效果

#### 淡入淡出
```python
# 淡入效果
fade_in_clip = clip.with_effects([vfx.FadeIn(1.0)])

# 淡出效果
fade_out_clip = clip.with_effects([vfx.FadeOut(1.0)])

# 交叉淡入淡出
cross_fade_in = clip.with_effects([vfx.CrossFadeIn(1.0)])
cross_fade_out = clip.with_effects([vfx.CrossFadeOut(1.0)])
```

#### 尺寸和裁剪
```python
# 调整大小
resized = clip.with_effects([vfx.Resize(width=640)])
resized = clip.with_effects([vfx.Resize(height=480)])
resized = clip.with_effects([vfx.Resize(0.5)])  # 缩放50%

# 裁剪
cropped = clip.with_effects([vfx.Crop(x1=100, y1=100, x2=500, y2=400)])
```

#### 颜色效果
```python
# 黑白效果
bw_clip = clip.with_effects([vfx.BlackAndWhite()])

# 颜色反转
inverted = clip.with_effects([vfx.InvertColors()])

# 伽马校正
gamma_corrected = clip.with_effects([vfx.GammaCorrection(gamma=1.5)])

# 亮度对比度
bright_clip = clip.with_effects([vfx.LumContrast(lum=50, contrast=1.2)])

# 颜色倍增
colored = clip.with_effects([vfx.MultiplyColor(factor=(1.2, 0.8, 1.0))])
```

#### 空间变换
```python
# 旋转
rotated = clip.with_effects([vfx.Rotate(45)])  # 旋转45度

# 镜像
mirror_x = clip.with_effects([vfx.MirrorX()])
mirror_y = clip.with_effects([vfx.MirrorY()])

# 边距
with_margin = clip.with_effects([vfx.Margin(40)])  # 添加40像素边距
```

#### 时间效果
```python
# 变速
fast_clip = clip.with_effects([vfx.MultiplySpeed(2.0)])  # 2倍速
slow_clip = clip.with_effects([vfx.MultiplySpeed(0.5)])  # 慢动作

# 时间镜像（倒放）
reversed_clip = clip.with_effects([vfx.TimeMirror()])

# 时间对称
symmetric = clip.with_effects([vfx.TimeSymmetrize()])

# 循环
looped = clip.with_effects([vfx.Loop(n=3)])  # 循环3次
```

#### 特殊效果
```python
# 闪烁
blinking = clip.with_effects([vfx.Blink(duration_on=0.5, duration_off=0.5)])

# 冻结帧
frozen = clip.with_effects([vfx.Freeze(t=5)])  # 冻结第5秒的帧

# 滑入滑出
slide_in = clip.with_effects([vfx.SlideIn(duration=1.0, side='left')])
slide_out = clip.with_effects([vfx.SlideOut(duration=1.0, side='right')])

# 滚动
scrolling = clip.with_effects([vfx.Scroll(w=50, h=50, x_speed=50, y_speed=30)])

# 绘画效果
painted = clip.with_effects([vfx.Painting(saturation=1.4, black=0.006)])
```

### 完整效果列表

| 效果名 | 说明 | 主要参数 |
|-------|------|----------|
| `AccelDecel` | 加速减速 | `abruptness` |
| `BlackAndWhite` | 黑白效果 | 无 |
| `Blink` | 闪烁效果 | `duration_on`, `duration_off` |
| `Crop` | 裁剪 | `x1`, `y1`, `x2`, `y2` |
| `CrossFadeIn` | 交叉淡入 | `duration` |
| `CrossFadeOut` | 交叉淡出 | `duration` |
| `EvenSize` | 偶数尺寸 | 无 |
| `FadeIn` | 淡入 | `duration` |
| `FadeOut` | 淡出 | `duration` |
| `Freeze` | 冻结帧 | `t`, `freeze_duration` |
| `FreezeRegion` | 冻结区域 | `t`, `region`, `outside` |
| `GammaCorrection` | 伽马校正 | `gamma` |
| `HeadBlur` | 头部模糊 | `fx`, `fy`, `r_zone`, `r_blur` |
| `InvertColors` | 颜色反转 | 无 |
| `Loop` | 循环 | `n`, `duration` |
| `LumContrast` | 亮度对比度 | `lum`, `contrast`, `contrast_thr` |
| `MakeLoopable` | 制作循环 | `cross_duration` |
| `Margin` | 边距 | `mar`, `color` |
| `MaskColor` | 遮罩颜色 | `color`, `thr`, `s` |
| `MasksAnd` | 遮罩与操作 | 无 |
| `MasksOr` | 遮罩或操作 | 无 |
| `MirrorX` | X轴镜像 | 无 |
| `MirrorY` | Y轴镜像 | 无 |
| `MultiplyColor` | 颜色倍增 | `factor` |
| `MultiplySpeed` | 速度倍增 | `factor` |
| `Painting` | 绘画效果 | `saturation`, `black` |
| `Resize` | 调整大小 | `newsize`, `height`, `width` |
| `Rotate` | 旋转 | `angle`, `unit`, `resample`, `expand` |
| `Scroll` | 滚动 | `w`, `h`, `x_speed`, `y_speed` |
| `SlideIn` | 滑入 | `duration`, `side` |
| `SlideOut` | 滑出 | `duration`, `side` |
| `SuperSample` | 超采样 | `d`, `nbox` |
| `TimeMirror` | 时间镜像 | 无 |
| `TimeSymmetrize` | 时间对称 | 无 |

---

## 音频效果 (afx)

### 导入方式
```python
from moviepy import afx
```

### 常用音频效果

```python
# 音频淡入淡出
audio_fade_in = clip.with_effects([afx.AudioFadeIn(1.0)])
audio_fade_out = clip.with_effects([afx.AudioFadeOut(1.0)])

# 音量调整
volume_adjusted = clip.with_effects([afx.MultiplyVolume(0.5)])

# 音频延迟
delayed_audio = clip.with_effects([afx.AudioDelay(0.5)])

# 音频标准化
normalized = clip.with_effects([afx.AudioNormalize()])
```

---

## 自定义滤镜

### 创建自定义效果

```python
import numpy as np

# 自定义亮度滤镜
def brightness_filter(get_frame, t):
    frame = get_frame(t)
    # 增加亮度
    bright_frame = frame * 1.3
    # 确保像素值在0-255范围内
    bright_frame = np.clip(bright_frame, 0, 255)
    return bright_frame.astype('uint8')

# 应用自定义滤镜
bright_clip = clip.transform(brightness_filter)

# 自定义时间变换
def oscillating_speed(t):
    # 振荡速度
    return t + 0.2 * np.sin(2 * np.pi * t)

oscillating_clip = clip.time_transform(oscillating_speed)
```

---

## 视频合成

### CompositeVideoClip
合成多个视频剪辑。

```python
from moviepy import CompositeVideoClip

# 基本合成
final_clip = CompositeVideoClip([
    video_clip1,
    video_clip2.with_position(('center', 'center')),
    text_clip.with_position(('center', 'bottom'))
])

# 设置背景大小
final_clip = CompositeVideoClip([
    video_clip1,
    video_clip2.with_position('center')
], size=(1920, 1080))
```

### 位置设置

```python
# 位置参数
clip.with_position('center')           # 居中
clip.with_position(('center', 'top'))  # 水平居中，顶部对齐
clip.with_position((100, 200))         # 绝对坐标 (x, y)

# 相对位置函数
def moving_position(t):
    return (50 + t * 100, 100)  # 水平移动

moving_clip = clip.with_position(moving_position)
```

### 音频合成

```python
from moviepy import CompositeAudioClip

# 音频混合
final_audio = CompositeAudioClip([
    video.audio,
    background_music.with_volume_scaled(0.3)
])

final_video = video.with_audio(final_audio)
```

---

## 导入方式

### 推荐导入
```python
# 主要类
from moviepy import (
    VideoFileClip, AudioFileClip,
    TextClip, ColorClip, ImageClip,
    CompositeVideoClip, CompositeAudioClip,
    concatenate_videoclips, concatenate_audioclips
)

# 效果模块
from moviepy import vfx  # 视频效果
from moviepy import afx  # 音频效果
```

### 传统导入（不推荐）
```python
# 避免使用，可能导入过时的API
from moviepy.editor import *
```

---

## 最佳实践

### 1. 资源管理
```python
# 使用 with 语句自动管理资源
with VideoFileClip("video.mp4") as video:
    processed = video.subclipped(0, 10).with_effects([vfx.FadeIn(1)])
    processed.write_videofile("output.mp4")
# 自动调用 close()

# 手动管理资源
video = VideoFileClip("video.mp4")
try:
    processed = video.subclipped(0, 10)
    processed.write_videofile("output.mp4")
finally:
    video.close()
```

### 2. 链式调用
```python
# 推荐：清晰的链式调用
result = (clip
    .subclipped(0, 10)
    .with_effects([vfx.FadeIn(1), vfx.FadeOut(1)])
    .resized(width=720)
    .with_position('center')
)
```

### 3. 效果应用
```python
# 推荐：使用 with_effects
clip.with_effects([
    vfx.FadeIn(1),
    vfx.Resize(0.5),
    vfx.FadeOut(1)
])

# 避免：多次单独应用
# clip.fx(vfx.fadein, 1).fx(vfx.resize, 0.5).fx(vfx.fadeout, 1)
```

### 4. 时间格式
```python
# 支持多种时间格式
clip.subclipped(0, 10)                    # 秒
clip.subclipped((1, 30), (2, 0))          # (分, 秒)
clip.subclipped('00:01:30', '00:02:00')   # 字符串
clip.subclipped(0, -5)                    # 负数表示从结尾
```

### 5. 性能优化
```python
# 设置适当的FPS
clip.write_videofile("output.mp4", fps=24)

# 使用多线程
clip.write_videofile("output.mp4", threads=4)

# 降低质量提高速度
clip.write_videofile("output.mp4", bitrate="500k")

# 使用临时音频文件
clip.write_videofile("output.mp4", temp_audiofile="temp.m4a")
```

### 6. 内存管理
```python
# 及时关闭不需要的剪辑
for clip in clips:
    clip.close()

# 避免创建过多中间变量
result = original_clip.subclipped(0, 10).with_effects([vfx.FadeIn(1)])
```

---

## 版本差异说明

### MoviePy 1.x vs 2.x 主要变化

| 功能 | 1.x (旧) | 2.x (新) |
|------|----------|----------|
| **导入** | `from moviepy.editor import *` | `from moviepy import VideoFileClip, ...` |
| **剪辑** | `clip.subclip(0, 10)` | `clip.subclipped(0, 10)` |
| **调整大小** | `clip.resize(width=640)` | `clip.resized(width=640)` |
| **文本创建** | `TextClip("text", fontsize=50)` | `TextClip(text="text", font_size=50)` |
| **设置时长** | `clip.set_duration(5)` | `clip.with_duration(5)` |
| **效果应用** | `clip.fx(vfx.resize, 0.5)` | `clip.with_effects([vfx.Resize(0.5)])` |
| **音量调整** | `clip.volumex(0.5)` | `clip.with_volume_scaled(0.5)` |

### 核心变化规律
1. **方法命名**: `set_*` → `with_*`
2. **效果应用**: 单个 `.fx()` → 列表 `.with_effects([])`
3. **参数命名**: 更加明确和一致
4. **返回值**: 所有 `with_*` 方法都返回新的剪辑对象

---

## 故障排除

### 常见问题
1. **FFmpeg 未找到**: 确保 FFmpeg 已安装并在 PATH 中
2. **内存不足**: 使用较小的分辨率或分段处理
3. **文件被锁定**: 确保调用了 `close()` 方法
4. **编码问题**: 指定正确的编码参数

### 调试技巧
```python
# 检查FFmpeg配置
from moviepy.config import check
check()

# 预览剪辑
clip.preview()  # 播放预览

# 保存单帧进行调试
clip.save_frame("debug_frame.png", t=5)
```

---

**这份文档基于 MoviePy 2.x 官方文档编写，确保了 API 使用的准确性和完整性。建议在开发时优先参考本文档！** 🎬✨ 