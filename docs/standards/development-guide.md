# 🛠️ Reavo Video Creator 开发规范

> 项目开发的统一标准和规范 - 确保代码质量和架构一致性

## 📋 目录
- [🏗️ 架构设计](#-架构设计)
- [💻 编码标准](#-编码标准)
- [🎬 MoviePy 使用规范](#-moviepy-使用规范)
- [🔧 开发流程](#-开发流程)
- [🧪 测试规范](#-测试规范)
- [📚 文档规范](#-文档规范)
- [⚡ 性能优化](#-性能优化)
- [🛡️ 安全规范](#-安全规范)

---

## 🏗️ 架构设计

### 总体架构
采用 **DDD（领域驱动设计）+ 分层架构** 模式：

```
📁 reavo_video_creator/
├── 🎯 表现层 (CLI Interface)
│   ├── main.py                 # 主命令行接口
│   └── cli_utils.py           # CLI工具函数
├── 🚀 应用服务层 (Application Services)
│   ├── video_generator.py     # 视频生成服务
│   ├── config_processor.py    # 配置处理服务
│   └── resource_manager.py    # 资源管理服务
├── 🏗️ 领域模型层 (Domain Models)
│   ├── template_renderer.py   # 模板渲染引擎
│   ├── config_validator.py    # 配置验证器
│   └── video_models.py        # 视频模型类
└── 🔧 基础设施层 (Infrastructure)
    ├── cache/                 # 缓存系统
    ├── temp/                  # 临时文件
    └── output/                # 输出目录
```

### 核心模块职责

#### 1. ConfigProcessor (配置处理器)
**职责**: 配置文件解析、验证和标准化
- 支持 V3 四层配置架构 (basic/intro/segments/outro)
- 集成完整的配置验证系统
- 提供质量预设管理
- 支持多种配置格式

#### 2. VideoGenerator (视频生成器)
**职责**: 视频生成流程控制和管理
- 协调各模块完成视频生成
- 处理渲染队列和并发控制
- 提供进度监控和错误处理
- 支持预览模式和批量处理

#### 3. TemplateRenderer (模板渲染引擎)
**职责**: 视频元素的具体渲染实现
- 智能字幕系统（自动换行、中英文混合）
- 多层视觉效果（音频、字幕、背景、头像、叠加）
- 特效和转场效果
- 时间轴管理和同步

#### 4. ResourceManager (资源管理器)
**职责**: 资源下载、缓存和管理
- 智能缓存系统
- 网络资源下载
- 本地资源管理
- 清理和优化机制

#### 5. ConfigValidator (配置验证器)
**职责**: 配置完整性和正确性验证
- 结构验证（必需字段、层级关系）
- 类型验证（数据类型、格式规范）
- 业务验证（取值范围、业务规则）
- 错误报告和修复建议

#### 6. CLI Interface (命令行接口)
**职责**: 用户交互和命令处理
- 命令解析和参数验证
- 进度显示和状态报告
- 错误处理和用户反馈
- 帮助信息和使用指南

### 数据流设计

```mermaid
graph TD
    A[配置文件] --> B[ConfigProcessor]
    B --> C[配置验证]
    C --> D[标准化配置]
    D --> E[VideoGenerator]
    E --> F[TemplateRenderer]
    F --> G[ResourceManager]
    G --> H[视频输出]
    
    I[用户命令] --> J[CLI Interface]
    J --> E
    
    K[缓存系统] --> G
    L[模板系统] --> F
```

---

## 💻 编码标准

### Python 代码规范

#### 1. 基本规范
- **Python 版本**: Python 3.9+
- **编码格式**: UTF-8
- **行长度**: 最大 120 字符
- **缩进**: 4 个空格，不使用 Tab
- **命名**: 遵循 PEP 8 规范

#### 2. 类型注解
**强制要求**: 所有函数和方法必须有类型注解

```python
# ✅ 正确示例
def process_video_config(config: Dict[str, Any]) -> StandardConfig:
    """处理视频配置"""
    pass

class VideoGenerator:
    def __init__(self, config: StandardConfig) -> None:
        self.config = config
    
    def generate(self) -> Path:
        """生成视频并返回输出路径"""
    pass

# ❌ 错误示例
def process_config(config):  # 缺少类型注解
    pass
```

#### 3. 文档字符串
**强制要求**: 所有公共函数和类必须有文档字符串

```python
def create_text_clip(
    text: str,
    duration: float,
    style: Optional[Dict[str, Any]] = None
) -> TextClip:
    """
    创建文本剪辑
    
    Args:
        text: 文本内容
        duration: 持续时间（秒）
        style: 可选的样式配置
        
    Returns:
        TextClip: 文本剪辑对象
        
    Raises:
        ValueError: 当文本为空时
        
    Example:
        >>> clip = create_text_clip("Hello", 3.0)
        >>> clip.duration
        3.0
    """
    pass
```

#### 4. 异常处理
**标准模式**: 使用具体的异常类型，提供清晰的错误信息

```python
# ✅ 正确示例
try:
    config = load_config(config_path)
except FileNotFoundError:
    raise ConfigError(f"配置文件不存在: {config_path}")
except json.JSONDecodeError as e:
    raise ConfigError(f"配置文件格式错误: {e}")
except Exception as e:
    logger.error(f"配置加载失败: {e}")
    raise

# ❌ 错误示例
try:
    config = load_config(config_path)
except:  # 过于宽泛的异常捕获
    pass
```

#### 5. 导入规范
**导入顺序**: 按以下顺序组织导入语句

```python
# 1. 标准库
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any

# 2. 第三方库
import moviepy
from moviepy.editor import VideoClip, TextClip

# 3. 本项目模块
from .config_processor import ConfigProcessor
from .video_models import StandardConfig
```

#### 6. 数据类设计
**使用 dataclass**: 用于数据结构定义

```python
@dataclass
class VideoConfig:
    """视频配置数据类"""
    filename: str
    quality: str = "1080p"
    fps: int = 30
    duration: Optional[float] = None
    
    def __post_init__(self):
        """数据验证和默认值设置"""
        if not self.filename:
            raise ValueError("文件名不能为空")
```

---

## 🎬 MoviePy 使用规范

### 版本要求
- **严格要求**: 仅使用 MoviePy 2.x API
- **禁止**: 使用任何 MoviePy 1.x 的废弃 API
- **参考**: 详见 [MoviePy API参考](../references/MoviePy_API_Document.md)

### 基本使用规范

#### 1. 导入规范
```python
# ✅ 正确的导入方式
from moviepy.editor import (
    VideoClip, AudioClip, TextClip, ImageClip,
    CompositeVideoClip, CompositeAudioClip,
    concatenate_videoclips, concatenate_audioclips
)

# ❌ 错误的导入方式
from moviepy import *  # 避免通配符导入
```

#### 2. 剪辑创建
```python
# ✅ 正确的剪辑创建
def create_text_clip(text: str, duration: float) -> TextClip:
    """创建文本剪辑"""
    return TextClip(
        text=text,
        duration=duration,
        fontsize=50,
        color='white',
        font='Arial'
    )

# ❌ 错误的方式
def create_text_clip(text, duration):
    # 缺少类型注解和参数验证
    return TextClip(text, duration=duration)
```

#### 3. 特效应用
```python
# ✅ 正确的特效应用
def apply_fade_effect(clip: VideoClip, fade_duration: float = 1.0) -> VideoClip:
    """应用淡入淡出效果"""
    return clip.crossfadein(fade_duration).crossfadeout(fade_duration)

# ❌ 错误的方式
def apply_fade_effect(clip):
    return clip.fx(fadeIn, 1).fx(fadeOut, 1)  # 旧版本API
```

#### 4. 资源管理
```python
# ✅ 正确的资源管理
def process_video_with_cleanup(video_path: Path) -> VideoClip:
    """处理视频并确保资源清理"""
    clip = None
try:
        clip = VideoFileClip(str(video_path))
        # 处理逻辑
        return clip
finally:
        if clip:
            clip.close()  # 确保资源释放
```

#### 5. 性能优化
```python
# ✅ 性能优化的导出参数
def export_video(clip: VideoClip, output_path: Path, quality: str = "high") -> None:
    """导出视频，使用优化参数"""
    quality_settings = {
        "standard": {
            "codec": "libx264",
            "bitrate": "2000k",
            "fps": 24
        },
        "high": {
            "codec": "libx264",
            "bitrate": "4500k",
            "fps": 30
        }
    }
    
    settings = quality_settings.get(quality, quality_settings["high"])
    clip.write_videofile(
        str(output_path),
        **settings,
        temp_audiofile="temp-audio.m4a",
        remove_temp=True
    )
```

---

## 🔧 开发流程

### 任务管理
- **必须**: 严格按照 TODO 列表进行开发
- **实时更新**: 完成任务后立即更新状态
- **文档同步**: 功能完成后更新相关文档

### 代码审查
1. **自我审查**: 提交前检查代码规范和类型注解
2. **功能测试**: 确保所有功能正常工作
3. **性能验证**: 检查渲染速度和内存使用
4. **文档更新**: 同步更新相关文档

### 版本控制
- **分支策略**: 功能分支 → 开发分支 → 主分支
- **提交信息**: 清晰描述变更内容和影响
- **标签管理**: 重要里程碑创建标签

---

## 🧪 测试规范

### 测试类型

#### 1. 单元测试
```python
def test_config_validation():
    """测试配置验证功能"""
    validator = ConfigValidator()
    
    # 测试有效配置
    valid_config = {"basic": {"output": {"filename": "test"}}}
    errors = validator.validate_config(valid_config)
    assert not errors
    
    # 测试无效配置
    invalid_config = {"basic": {}}
    errors = validator.validate_config(invalid_config)
    assert errors
```

#### 2. 集成测试
```python
def test_video_generation_flow():
    """测试完整视频生成流程"""
    config_path = Path("test_config.json")
    processor = ConfigProcessor()
    generator = VideoGenerator()
    
    # 加载配置
    config = processor.load_config(config_path)
    
    # 生成视频
    output_path = generator.generate(config)
    
    # 验证输出
    assert output_path.exists()
    assert output_path.suffix == ".mp4"
```

#### 3. 性能测试
```python
def test_rendering_performance():
    """测试渲染性能"""
    start_time = time.time()
    
    # 执行渲染
    render_video(config)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 验证性能要求
    assert duration < 60  # 应在60秒内完成
```

### 测试数据管理
- **测试文件**: 使用 `test_` 前缀
- **测试数据**: 存放在 `test/` 目录
- **清理机制**: 测试后自动清理临时文件

---

## 📚 文档规范

### 文档结构
严格遵循 [文档体系规范](../README.md) 的要求：
- **单一职责**: 每个分类只有一个主文档
- **内容完整**: 主文档包含该分类的全部信息
- **结构清晰**: 统一的格式和组织方式

### 文档更新规则
- **功能完成**: 立即更新相关文档
- **API 变更**: 同步更新 API 文档
- **配置变更**: 更新使用指南
- **架构调整**: 更新开发规范

### 文档质量标准
- **准确性**: 信息必须准确和最新
- **完整性**: 示例代码必须可执行
- **一致性**: 各文档间保持一致
- **可读性**: 清晰的结构和用户友好的语言

---

## ⚡ 性能优化

### 渲染优化
```python
# 优化的视频导出参数
QUALITY_PRESETS = {
    "standard": {
        "codec": "libx264",
        "bitrate": "2000k",
        "preset": "faster",
        "crf": 26
    },
    "high": {
        "codec": "libx264",
        "bitrate": "4500k", 
        "preset": "medium",
        "crf": 23
    }
}
```

### 内存管理
- **及时释放**: 使用完毕后立即释放剪辑对象
- **缓存优化**: 智能缓存常用资源
- **批量处理**: 优化大量文件的处理流程

### 并发处理
- **线程池**: 用于 I/O 密集型任务
- **进程池**: 用于 CPU 密集型任务
- **异步处理**: 用于网络请求

---

## 🛡️ 安全规范

### 文件处理
```python
def safe_file_operation(file_path: Path) -> None:
    """安全的文件操作"""
    # 路径验证
    if not file_path.is_file():
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    # 权限检查
    if not os.access(file_path, os.R_OK):
        raise PermissionError(f"没有读取权限: {file_path}")
```

### 输入验证
```python
def validate_user_input(config: Dict[str, Any]) -> None:
    """验证用户输入"""
    # 使用 ConfigValidator 进行完整验证
    validator = ConfigValidator()
    errors = validator.validate_config(config)
    
    if errors:
        raise ValueError(f"输入验证失败: {errors}")
```

### 错误处理
- **不暴露敏感信息**: 错误信息不包含系统路径
- **日志记录**: 详细记录操作日志
- **异常恢复**: 提供合理的错误恢复机制

---

## 🎯 开发最佳实践

### 1. 代码质量
- **类型安全**: 使用类型注解和类型检查
- **代码复用**: 提取公共功能为工具函数
- **简洁明了**: 优先可读性而非简洁性

### 2. 架构设计
- **职责清晰**: 每个模块职责单一明确
- **低耦合**: 模块间依赖最小化
- **高内聚**: 相关功能集中管理

### 3. 持续改进
- **性能监控**: 定期检查渲染性能
- **用户反馈**: 收集和处理用户建议
- **技术更新**: 跟进 MoviePy 和 Python 版本

---

## 📊 质量指标

### 代码质量
- **类型覆盖率**: 100% 的函数有类型注解
- **文档覆盖率**: 100% 的公共 API 有文档
- **测试覆盖率**: 90%+ 的核心功能有测试

### 性能指标
- **渲染速度**: 比基准版本快 60%+
- **内存使用**: 保持在合理范围内
- **错误率**: 配置验证的零错误容忍

### 用户体验
- **上手时间**: 新用户 15 分钟内完成第一个视频
- **文档查找**: 常见问题能在 3 分钟内找到答案
- **错误处理**: 提供清晰的错误信息和解决建议

---

**⚠️ 重要提醒**: 
1. 严格遵循 MoviePy 2.x API 规范
2. 实时更新 TODO 列表状态
3. 保持文档与代码同步
4. 优先代码质量而非开发速度

---

**📅 最后更新**: 2025-01-07  
**📊 规范版本**: v1.0  
**🎯 适用**: 所有 Reavo Video Creator 开发者

---

**严格遵循开发规范，确保项目质量和可维护性！** 🛠️✨ 