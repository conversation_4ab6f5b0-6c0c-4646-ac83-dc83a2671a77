# 测试规则和指南

## 🚫 禁止事项

### 1. 禁止创建简单测试配置
- **不允许**创建简单的测试配置文件（如只有基本字段的JSON）
- **不允许**生成临时的、功能不完整的配置文件
- **不允许**使用占位符URL或虚假数据的配置

### 2. 禁止创建测试专用文件夹
- **不允许**创建 `test_configs/` 文件夹
- **不允许**创建 `test_images/` 文件夹
- **不允许**在项目根目录创建任何测试相关文件

## ✅ 必须遵循的规则

### 1. 只使用 examples 中的配置文件
- **必须**使用 `examples/` 目录中的预设配置文件进行测试
- **必须**使用完整的、真实的配置文件，包含：
  - 真实的音频URL
  - 真实的图片URL
  - 完整的场景配置
  - 完整的动画配置
  - 完整的字幕配置

### 2. 可用的测试配置文件
```
examples/
├── real_estate_showcase.json          # 豪华房地产展示
├── real_estate_showcase_enhanced.json # 增强版房地产展示
├── quick_start.json                   # 快速开始示例
├── product_showcase.json              # 产品展示
└── ... (其他完整配置文件)
```

### 3. 测试命令格式
```bash
# 正确的测试命令
python3 main.py generate examples/real_estate_showcase.json
python3 main.py generate examples/real_estate_showcase_enhanced.json

# 错误的测试命令（禁止）
python3 main.py generate test_configs/simple_test.json
python3 main.py generate my_test.json
```

## 📋 测试流程

### 1. 功能验证测试
当需要验证新功能时：
1. 选择最相关的 `examples/` 配置文件
2. 运行完整的视频生成流程
3. 检查输出视频的效果
4. 验证日志中的功能执行情况

### 2. 黑边修复测试
```bash
# 使用真实的房地产配置测试黑边修复
python3 main.py generate examples/real_estate_showcase.json
```
验证点：
- 日志中显示图片适配信息
- 输出视频无黑边
- 图片填满整个画面

### 3. 动画效果测试
```bash
# 使用增强版配置测试动画效果
python3 main.py generate examples/real_estate_showcase_enhanced.json
```
验证点：
- 房地产主题动画正确应用
- 动画循环系统正常工作
- 动画参数在合理范围内

### 4. 性能测试
```bash
# 使用完整配置测试性能
python3 main.py generate examples/real_estate_showcase.json
```
验证点：
- 视频生成时间合理
- 内存使用正常
- 临时文件正确清理

## 🎯 测试目标

### 1. 完整功能验证
- 使用真实数据验证所有功能
- 确保配置文件的复杂性能够暴露潜在问题
- 验证实际使用场景下的表现

### 2. 真实场景模拟
- 使用真实的音频和图片资源
- 测试完整的视频生成流程
- 验证用户实际使用体验

### 3. 质量保证
- 确保输出视频质量符合预期
- 验证所有动画效果正确应用
- 检查字幕、音频同步等细节

## 🔧 调试和问题排查

### 1. 日志分析
- 关注图片适配日志：`🖼️ 图片原始尺寸`、`✅ 图片适配完成`
- 关注动画应用日志：`🎬 应用预设动画`、`🏠 使用房地产动画`
- 关注错误和警告信息

### 2. 输出验证
- 检查生成的视频文件
- 验证视频时长和分辨率
- 确认动画效果和画面质量

### 3. 性能监控
- 监控生成时间
- 检查内存使用情况
- 验证资源清理是否完整

## 📝 测试报告

每次测试后应记录：
1. **使用的配置文件**：`examples/xxx.json`
2. **测试目的**：验证什么功能
3. **测试结果**：成功/失败，具体表现
4. **发现的问题**：如有问题，详细描述
5. **改进建议**：如有改进空间，提出建议

## 🚀 最佳实践

### 1. 测试前准备
- 确保网络连接正常（需要下载真实资源）
- 检查磁盘空间充足
- 清理之前的输出文件

### 2. 测试执行
- 使用完整的配置文件
- 观察完整的生成过程
- 记录关键日志信息

### 3. 测试后验证
- 播放生成的视频文件
- 检查视频质量和效果
- 验证功能是否按预期工作

## 📚 配置文件说明

### real_estate_showcase.json
- **用途**：测试房地产视频生成的完整流程
- **特点**：包含多个场景、真实图片、完整音频
- **适用**：黑边修复、动画效果、字幕处理等功能测试

### real_estate_showcase_enhanced.json
- **用途**：测试增强版房地产功能
- **特点**：更复杂的动画配置、更多场景
- **适用**：高级动画、性能测试、复杂场景处理

通过遵循这些规则，我们能够确保测试的有效性和真实性，避免因简单测试配置而遗漏的问题。
