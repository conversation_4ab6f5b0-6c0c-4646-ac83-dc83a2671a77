#!/usr/bin/env python3
"""
Reavo Video Creator - 配置处理器 V3
支持新的四层配置结构：basic, intro, segments, outro
"""

import json
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
from config_validator import ConfigValidator

# ================== 新的四层配置数据类 ==================

@dataclass
class BasicOutputConfig:
    """基础输出配置"""
    filename: str
    quality: str = "1080p"
    resolution: List[int] = None
    
    def __post_init__(self):
        if self.resolution is None:
            # 根据quality设置默认分辨率
            quality_map = {
                "720p": [1280, 720],
                "1080p": [1920, 1080],
                "4k": [3840, 2160]
            }
            self.resolution = quality_map.get(self.quality, [1920, 1080])

@dataclass
class BasicAudioConfig:
    """基础音频配置"""
    background: Dict[str, Any] = None  # {music: str, volume: float}
    narration: Dict[str, Any] = None   # {volume: float}
    music: Dict[str, Any] = None       # {intro: str, outro: str, volume: float}
    effects: Dict[str, Any] = None     # {transition: str, click: str, success: str}
    
    def __post_init__(self):
        if self.background is None:
            self.background = {"music": None, "volume": 0.3}
        if self.narration is None:
            self.narration = {"volume": 1.0}
        if self.music is None:
            self.music = {"intro": None, "outro": None, "volume": 0.8}
        if self.effects is None:
            self.effects = {"transition": None, "click": None, "success": None}

@dataclass
class BasicConfig:
    """基础配置"""
    output: BasicOutputConfig
    audio: BasicAudioConfig

@dataclass
class IntroOutroConfig:
    """Intro/Outro配置"""
    duration: float
    title: Dict[str, Any]      # {text: str, style: dict}
    subtitle: Dict[str, Any]   # {text: str, style: dict}
    background: Dict[str, Any] # {type: str, value: str}
    animation: Dict[str, Any]  # {text_effect: str, transition_in/out: str}

@dataclass
class SceneAudioConfig:
    """场景音频配置"""
    url: str
    duration: float

@dataclass
class SceneCaptionConfig:
    """场景字幕配置 (已重命名为caption)"""
    text: str
    style: Optional[Dict[str, Any]] = None
    animation: Optional[Dict[str, Any]] = None
    position: Optional[str] = None

@dataclass
class SceneBackgroundConfig:
    """场景背景配置"""
    type: str  # "image", "video", "color"
    url: Optional[str] = None
    value: Optional[str] = None  # 颜色值
    animation: Optional[Dict[str, Any]] = None

@dataclass
class SceneAvatarConfig:
    """场景头像配置"""
    url: str

@dataclass
class SceneOverlayConfig:
    """场景叠加配置"""
    id: str
    type: str  # "text", "image", "video"
    content: str
    position: str
    offset: Dict[str, Any]  # {x: int, y: int}
    style: Dict[str, Any]
    timing: Dict[str, Any]  # {start: float, duration: float}
    animation: Dict[str, Any]  # {entrance: str, exit: str}

@dataclass
class SceneConfig:
    """场景配置"""
    id: str
    audio: SceneAudioConfig
    caption: SceneCaptionConfig
    background: SceneBackgroundConfig
    avatar: Optional[SceneAvatarConfig] = None
    overlays: Optional[List[SceneOverlayConfig]] = None

@dataclass
class SegmentConfig:
    """片段配置"""
    id: str
    title: str
    scenes: List[SceneConfig]

@dataclass
class StandardConfig:
    """标准化的完整配置"""
    basic: BasicConfig
    intro: Optional[IntroOutroConfig] = None
    segments: List[SegmentConfig] = None
    outro: Optional[IntroOutroConfig] = None
    raw_config: Optional[Dict[str, Any]] = None  # 保存原始配置

class ConfigProcessor:
    """配置处理器 V3 - 支持新的四层配置结构"""
    
    def __init__(self):
        """初始化配置处理器"""
        self.base_dir = Path(__file__).parent
        self.validator = ConfigValidator()
        
        # 视频质量预设 - 三档优化配置
        self.quality_presets = {
            # 普通视频 - 画质中等、体积小、速度快
            "standard": {
                "name": "标准质量",
                "description": "适合日常分享，文件小，处理快",
                "resolution": [1280, 720],
                "fps": 24,
                "bitrate": "2000k",
                "encoder": "libx264",
                "preset": "faster",
                "crf": 26,
                "profile": "main"
            },
            # 高清视频 - 画质高、体积速度一般
            "high": {
                "name": "高清质量", 
                "description": "平衡画质与文件大小，推荐选择",
                "resolution": [1920, 1080],
                "fps": 30,
                "bitrate": "4500k",
                "encoder": "libx264",
                "preset": "medium",
                "crf": 23,
                "profile": "high"
            },
            # 4K视频 - 画质极高、体积大、速度慢
            "ultra": {
                "name": "超高清质量",
                "description": "最佳画质，适合专业用途，文件大",
                "resolution": [3840, 2160],
                "fps": 30,
                "bitrate": "12000k",
                "encoder": "libx264",
                "preset": "slow",
                "crf": 20,
                "profile": "high"
            },
            
            # 向后兼容的旧格式（建议迁移到新格式）
            "720p": {
                "name": "720P (兼容)",
                "description": "兼容旧配置，建议使用 standard",
                "resolution": [1280, 720],
                "fps": 30,
                "bitrate": "2500k",
                "encoder": "libx264",
                "preset": "medium",
                "crf": 25,
                "profile": "main"
            },
            "1080p": {
                "name": "1080P (兼容)",
                "description": "兼容旧配置，建议使用 high",
                "resolution": [1920, 1080],
                "fps": 30,
                "bitrate": "5000k",
                "encoder": "libx264",
                "preset": "medium",
                "crf": 23,
                "profile": "high"
            },
            "4k": {
                "name": "4K (兼容)",
                "description": "兼容旧配置，建议使用 ultra",
                "resolution": [3840, 2160],
                "fps": 30,
                "bitrate": "15000k",
                "encoder": "libx264",
                "preset": "medium",
                "crf": 21,
                "profile": "high"
            }
        }
        
        print("⚙️  配置处理器 V3 初始化完成")
    
    def load_config(self, config_path: Path) -> StandardConfig:
        """
        加载配置文件并标准化
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            标准化的配置对象
        """
        try:
            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                raw_config = json.load(f)
            
            print(f"📄 加载配置文件: {config_path.name}")
            
            # 验证配置结构
            validation_errors = self.validator.validate_config(raw_config)
            if validation_errors:
                print("❌ 配置验证失败:")
                for error in validation_errors:
                    print(f"  - {error}")
                raise ValueError(f"配置验证失败，发现 {len(validation_errors)} 个错误")
            
            # 处理四层配置结构
            return self._process_v3_config(raw_config)
                
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            raise
    
    def _process_v3_config(self, config: Dict[str, Any]) -> StandardConfig:
        """
        处理V3四层配置结构
        
        Args:
            config: 原始配置
            
        Returns:
            标准化配置
        """
        print("🎨 处理四层配置结构 (basic, intro, segments, outro)")
        
        # 处理basic配置
        basic_config = self._process_basic_config(config.get("basic", {}))
        
        # 处理intro配置
        intro_config = None
        if "intro" in config:
            intro_config = self._process_intro_outro_config(config["intro"])
        
        # 处理segments配置
        segments_config = []
        if "segments" in config:
            segments_config = self._process_segments_config(config["segments"])
        
        # 处理outro配置
        outro_config = None
        if "outro" in config:
            outro_config = self._process_intro_outro_config(config["outro"])
        
        return StandardConfig(
            basic=basic_config,
            intro=intro_config,
            segments=segments_config,
            outro=outro_config,
            raw_config=config
        )
    
    def _process_basic_config(self, basic: Dict[str, Any]) -> BasicConfig:
        """处理basic配置"""
        print("🔧 处理basic配置")
        
        # 处理output配置
        output_data = basic.get("output", {})
        output_config = BasicOutputConfig(
            filename=output_data.get("filename", "untitled"),
            quality=output_data.get("quality", "1080p"),
            resolution=output_data.get("resolution")
        )
        
        # 处理audio配置
        audio_data = basic.get("audio", {})
        audio_config = BasicAudioConfig(
            background=audio_data.get("background"),
            narration=audio_data.get("narration"),
            music=audio_data.get("music"),
            effects=audio_data.get("effects")
        )
        
        return BasicConfig(
            output=output_config,
            audio=audio_config
        )
    
    def _process_intro_outro_config(self, data: Dict[str, Any]) -> IntroOutroConfig:
        """处理intro/outro配置"""
        return IntroOutroConfig(
            duration=data.get("duration", 3.0),
            title=data.get("title", {}),
            subtitle=data.get("subtitle", {}),
            background=data.get("background", {}),
            animation=data.get("animation", {})
        )
    
    def _process_segments_config(self, segments: List[Dict[str, Any]]) -> List[SegmentConfig]:
        """处理segments配置"""
        print(f"🎬 处理 {len(segments)} 个segments")
        
        result = []
        for segment in segments:
            # 处理scenes
            scenes = []
            for scene_data in segment.get("scenes", []):
                scene = self._process_scene_config(scene_data)
                scenes.append(scene)
            
            segment_config = SegmentConfig(
                id=segment.get("id", str(uuid.uuid4())),
                title=segment.get("title", "Untitled Segment"),
                scenes=scenes
            )
            result.append(segment_config)
        
        return result
    
    def _process_scene_config(self, scene_data: Dict[str, Any]) -> SceneConfig:
        """处理单个scene配置"""
        # 处理audio
        audio_data = scene_data.get("audio", {})
        audio_config = SceneAudioConfig(
            url=audio_data.get("url", ""),
            duration=audio_data.get("duration", 3.0)
        )
        
        # 处理caption (字幕)
        caption_data = scene_data.get("caption", {})
        caption_config = SceneCaptionConfig(
            text=caption_data.get("text", ""),
            style=caption_data.get("style"),
            animation=caption_data.get("animation"),
            position=caption_data.get("position")
        )
        
        # 处理background
        background_data = scene_data.get("background", {})
        background_config = SceneBackgroundConfig(
            type=background_data.get("type", "color"),
            url=background_data.get("url"),
            value=background_data.get("value", "#000000"),
            animation=background_data.get("animation")
        )
        
        # 处理avatar
        avatar_config = None
        if "avatar" in scene_data:
            avatar_data = scene_data["avatar"]
            avatar_config = SceneAvatarConfig(
                url=avatar_data.get("url", "")
            )
        
        # 处理overlays
        overlays_config = []
        if "overlays" in scene_data:
            for overlay_data in scene_data["overlays"]:
                overlay_config = SceneOverlayConfig(
                    id=overlay_data.get("id", str(uuid.uuid4())),
                    type=overlay_data.get("type", "text"),
                    content=overlay_data.get("content", ""),
                    position=overlay_data.get("position", "center"),
                    offset=overlay_data.get("offset", {"x": 0, "y": 0}),
                    style=overlay_data.get("style", {}),
                    timing=overlay_data.get("timing", {}),
                    animation=overlay_data.get("animation", {})
                )
                overlays_config.append(overlay_config)
        
        return SceneConfig(
            id=scene_data.get("id", str(uuid.uuid4())),
            audio=audio_config,
            caption=caption_config,
            background=background_config,
            avatar=avatar_config,
            overlays=overlays_config if overlays_config else None
        )
    
    def validate_config_file(self, config_path: Path) -> List[str]:
        """
        验证配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            错误列表，空列表表示验证通过
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                raw_config = json.load(f)
            
            return self.validator.validate_config(raw_config)
            
        except Exception as e:
            return [f"配置文件读取失败: {str(e)}"]
    
    def get_quality_preset(self, quality: str) -> Dict[str, Any]:
        """获取视频质量预设"""
        return self.quality_presets.get(quality, self.quality_presets["high"])
    
    def get_all_quality_presets(self) -> Dict[str, Dict[str, Any]]:
        """获取所有质量预设"""
        return self.quality_presets
    
    def get_recommended_quality_presets(self) -> Dict[str, Dict[str, Any]]:
        """获取推荐的质量预设（新格式）"""
        return {
            "standard": self.quality_presets["standard"],
            "high": self.quality_presets["high"],
            "ultra": self.quality_presets["ultra"]
        }
    
    def get_quality_comparison(self) -> List[Dict[str, Any]]:
        """获取质量预设对比信息"""
        presets = self.get_recommended_quality_presets()
        
        comparison = []
        for quality_key, preset in presets.items():
            file_size_factor = {
                "standard": 1.0,
                "high": 2.5,
                "ultra": 6.0
            }
            
            render_time_factor = {
                "standard": 1.0,
                "high": 2.0,
                "ultra": 4.5
            }
            
            comparison.append({
                "quality": quality_key,
                "name": preset["name"],
                "description": preset["description"],
                "resolution": f"{preset['resolution'][0]}x{preset['resolution'][1]}",
                "fps": preset["fps"],
                "bitrate": preset["bitrate"],
                "file_size_factor": file_size_factor[quality_key],
                "render_time_factor": render_time_factor[quality_key],
                "recommended_for": {
                    "standard": ["社交媒体分享", "快速预览", "移动端观看"],
                    "high": ["网络发布", "电脑观看", "一般用途"],
                    "ultra": ["专业制作", "大屏播放", "存档保存"]
                }[quality_key]
            })
        
        return comparison
    
    def save_config(self, config: StandardConfig, output_path: Path):
        """保存配置到文件"""
        try:
            # 转换为字典格式
            config_dict = {
                "basic": asdict(config.basic),
                "intro": asdict(config.intro) if config.intro else None,
                "segments": [asdict(segment) for segment in config.segments] if config.segments else [],
                "outro": asdict(config.outro) if config.outro else None
            }
            
            # 去掉None值
            config_dict = {k: v for k, v in config_dict.items() if v is not None}
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, ensure_ascii=False, indent=2)
            
            print(f"💾 配置保存成功: {output_path}")
            
        except Exception as e:
            print(f"❌ 配置保存失败: {e}")
            raise
    
    def get_config_summary(self, config: StandardConfig) -> Dict[str, Any]:
        """获取配置摘要"""
        summary = {
            "filename": config.basic.output.filename,
            "quality": config.basic.output.quality,
            "resolution": config.basic.output.resolution,
            "has_intro": config.intro is not None,
            "has_outro": config.outro is not None,
            "segments_count": len(config.segments) if config.segments else 0,
            "total_scenes": sum(len(seg.scenes) for seg in config.segments) if config.segments else 0
        }
        
        return summary

# 导出主要类
__all__ = ['ConfigProcessor', 'StandardConfig', 'BasicConfig', 'SegmentConfig', 'SceneConfig', 'SceneCaptionConfig'] 