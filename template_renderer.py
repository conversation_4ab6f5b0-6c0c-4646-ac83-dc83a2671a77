#!/usr/bin/env python3
"""
Reavo Video Creator - 模板渲染器
使用 MoviePy 2.x API 渲染视频元素
"""

import os
import sys
import math
from pathlib import Path
import numpy as np
import cv2
from typing import Dict, Any, List, Tuple, Optional
from moviepy import (
    VideoFileClip, VideoClip, ImageClip, ColorClip,
    CompositeVideoClip, CompositeAudioClip, TextClip, AudioFileClip, AudioClip, vfx, afx
)

from resource_manager import ResourceManager
from config_processor import ConfigProcessor

try:
    from .config_processor import StandardConfig, SegmentConfig, SceneBackgroundConfig
    from .resource_manager import ResourceManager
except ImportError:
    from config_processor import StandardConfig, SegmentConfig, SceneBackgroundConfig
    from resource_manager import ResourceManager

# 导入增强动画渲染器
try:
    from enhanced_animation_renderer import enhanced_animation_renderer
    ENHANCED_ANIMATION_AVAILABLE = True
    print("✅ 增强动画渲染器已加载")
except ImportError as e:
    print(f"⚠️  增强动画渲染器未找到: {e}")
    ENHANCED_ANIMATION_AVAILABLE = False

# 导入中文字体配置工具
try:
    # 尝试从utils导入字体配置
    sys.path.append(str(Path(__file__).parent.parent))
    from utils.font_config import FontConfig
    FONT_CONFIG_AVAILABLE = True
except ImportError:
    print("⚠️  字体配置工具不可用，将使用系统默认字体")
    FONT_CONFIG_AVAILABLE = False

class TemplateRenderer:
    """模板渲染器 - 使用MoviePy渲染视频元素"""
    
    def __init__(self, resource_manager: ResourceManager):
        """
        初始化模板渲染器
        
        Args:
            resource_manager: 资源管理器
        """
        self.resource_manager = resource_manager
        self.font_path = None  # 默认字体路径
        
        # 初始化中文字体支持
        self._setup_chinese_font()
        
        print("🎨 模板渲染器初始化完成")
    
    def _setup_chinese_font(self):
        """设置中文字体支持"""
        self.font_path = None
        self.font_config = None
        
        if FONT_CONFIG_AVAILABLE:
            try:
                print("🔤 初始化中文字体系统...")
                self.font_config = FontConfig()
                
                # 获取推荐的中文字体
                recommended_font = self.font_config.get_recommended_font()
                
                if recommended_font:
                    self.font_path = recommended_font
                    print(f"✅ 中文字体配置成功: {Path(self.font_path).name}")
                else:
                    print("⚠️  未找到中文字体，尝试下载...")
                    downloaded_font = self.font_config.download_font('NotoSansCJK')
                    if downloaded_font:
                        self.font_path = downloaded_font
                        print(f"✅ 中文字体下载成功: {Path(self.font_path).name}")
                    else:
                        print("❌ 中文字体下载失败，将使用系统默认字体")
                        self.font_path = None
                        
            except Exception as e:
                print(f"⚠️  字体配置过程中出现问题: {e}")
                self.font_path = None
        else:
            print("⚠️  使用系统默认字体，可能影响中文显示效果")
    
    def render_intro(self, config: StandardConfig) -> Optional[VideoClip]:
        """渲染片头"""
        try:
            if not config.intro:
                return None
            
            print("🎬 渲染片头")
            
            duration = config.intro.duration
            
            # 创建背景
            background = self._create_background(config.intro.background, config.basic.output.resolution, duration)
            
            clips = [background]
            
            # 添加主标题
            if config.intro.title and config.intro.title.get("text"):
                title_clip = self._create_text_clip(
                    config.intro.title["text"],
                    config.intro.title.get("style", {}),
                    config.basic.output.resolution,
                    duration
                )
                clips.append(title_clip)
            
            # 添加副标题
            if config.intro.subtitle and config.intro.subtitle.get("text"):
                subtitle_clip = self._create_text_clip(
                    config.intro.subtitle["text"],
                    config.intro.subtitle.get("style", {}),
                    config.basic.output.resolution,
                    duration
                )
                clips.append(subtitle_clip)
            
            # 合成片头
            intro_clip = CompositeVideoClip(clips, size=config.basic.output.resolution)
            
            # 应用特效
            intro_clip = self._apply_intro_outro_effects(intro_clip, config.intro.animation)
            
            print("✅ 片头渲染完成")
            return intro_clip
            
        except Exception as e:
            print(f"❌ 片头渲染失败: {str(e)}")
            return None
    
    def render_outro(self, config: StandardConfig) -> Optional[VideoClip]:
        """渲染片尾"""
        try:
            if not config.outro:
                return None
            
            print("🎬 渲染片尾")
            
            duration = config.outro.duration
            
            # 创建背景
            background = self._create_background(config.outro.background, config.basic.output.resolution, duration)
            
            clips = [background]
            
            # 添加主文本 (title)
            if config.outro.title and config.outro.title.get("text"):
                main_text_clip = self._create_text_clip(
                    config.outro.title["text"],
                    config.outro.title.get("style", {}),
                    config.basic.output.resolution,
                    duration
                )
                clips.append(main_text_clip)
            
            # 添加副文本 (subtitle)
            if config.outro.subtitle and config.outro.subtitle.get("text"):
                sub_text_clip = self._create_text_clip(
                    config.outro.subtitle["text"],
                    config.outro.subtitle.get("style", {}),
                    config.basic.output.resolution,
                    duration
                )
                clips.append(sub_text_clip)
            
            # 合成片尾
            outro_clip = CompositeVideoClip(clips, size=config.basic.output.resolution)
            
            # 应用特效
            outro_clip = self._apply_intro_outro_effects(outro_clip, config.outro.animation)
            
            print("✅ 片尾渲染完成")
            return outro_clip
            
        except Exception as e:
            print(f"❌ 片尾渲染失败: {str(e)}")
            return None
    
    def render_content_segment(self, segment: SegmentConfig, config: StandardConfig) -> Optional[VideoClip]:
        """渲染内容片段"""
        try:
            # 计算片段总时长（所有场景的音频时长之和）
            total_duration = sum(scene.audio.duration for scene in segment.scenes)
            
            # 创建基础视频背景（使用第一个场景的背景）
            if segment.scenes:
                first_scene = segment.scenes[0]
                base_video = self._create_scene_background(first_scene.background, config.basic.output.resolution, total_duration)
            else:
                # 如果没有场景，创建默认背景
                base_video = ColorClip(size=config.basic.output.resolution, color=(44, 62, 80), duration=3.0)
            
            if not base_video:
                print(f"❌ 无法创建基础视频片段")
                return None
            
            # 创建字幕（基于所有场景的字幕）- 支持新的字段命名
            all_subtitles = []
            current_time = 0.0
            
            for scene in segment.scenes:
                # 检查caption字幕配置
                subtitle_text = None
                subtitle_config = None
                
                # 使用新的caption字段
                if hasattr(scene, 'caption') and scene.caption:
                    # scene.caption是SceneCaptionConfig对象
                    subtitle_text = scene.caption.text
                    
                    # 构建字幕配置
                    subtitle_config = {
                        "text": subtitle_text,
                        "font_size": 42,
                        "color": "white",
                        "stroke_color": "black",
                        "stroke_width": 2,
                        "position": scene.caption.position or "auto"
                    }
                    
                    # 如果有样式配置，合并样式
                    if scene.caption.style:
                        subtitle_config.update(scene.caption.style)
                    
                    # 如果有动画配置，添加动画
                    if scene.caption.animation:
                        subtitle_config["animation"] = scene.caption.animation
                
                if subtitle_text:
                    # 构建增强的字幕数据结构
                    subtitle_data = {
                        "text": subtitle_text,
                        "start_time": current_time,
                        "duration": scene.audio.duration,
                        "position": subtitle_config.get("position", "auto"),
                        "style": {
                            "font_size": subtitle_config.get("font_size", 42),
                            "color": subtitle_config.get("color", "white"),
                            "stroke_color": subtitle_config.get("stroke_color", "black"),
                            "stroke_width": subtitle_config.get("stroke_width", 2),
                            "background_color": subtitle_config.get("background_color", None)
                        },
                        "animation": subtitle_config.get("animation", {
                            "type": "fade_in",
                            "fade_in_duration": 0.5,
                            "fade_out_duration": 0.3
                        })
                    }
                    
                    all_subtitles.append(subtitle_data)
                    print(f"📝 添加字幕: {current_time:.2f}s-{current_time + scene.audio.duration:.2f}s '{subtitle_text[:30]}{'...' if len(subtitle_text) > 30 else ''}'")
                
                current_time += scene.audio.duration
            
            # 创建字幕剪辑
            if all_subtitles:
                subtitle_clips = self._create_subtitles_from_data(all_subtitles, config)
                if subtitle_clips:
                    all_clips = [base_video] + subtitle_clips
                    base_video = CompositeVideoClip(all_clips)
                    print(f"✅ 成功添加 {len(subtitle_clips)} 个字幕片段")
            
            print(f"✅ 成功渲染内容片段: {segment.id} (时长: {total_duration:.2f}s)")
            return base_video
            
        except Exception as e:
            print(f"❌ 渲染内容片段失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def _create_background(self, background_config: Dict[str, Any], resolution: List[int], duration: float) -> VideoClip:
        """创建背景视频"""
        bg_type = background_config.get("type", "color")
        
        if bg_type == "color":
            color = background_config.get("value", "#000000")
            # 将十六进制颜色转换为RGB
            color_rgb = self._hex_to_rgb(color)
            return ColorClip(size=resolution, color=color_rgb, duration=duration)
        
        elif bg_type == "image":
            image_path = self.resource_manager.get_resource(background_config.get("value"))
            if image_path:
                return ImageClip(str(image_path), duration=duration).resized(resolution)
        
        elif bg_type == "video":
            video_path = self.resource_manager.get_resource(background_config.get("value"))
            if video_path:
                return VideoFileClip(str(video_path)).subclipped(0, duration).resized(resolution)
        
        # 默认返回黑色背景
        return ColorClip(size=resolution, color=(0, 0, 0), duration=duration)
    
    def _create_text_clip(self, text: str, style: Dict[str, Any], resolution: List[int], duration: float) -> VideoClip:
        """创建文本剪辑，优化中文字符支持"""
        # 处理特殊字符兼容性
        text = self._sanitize_text(text)
        
        # 文本样式配置
        font_size = style.get("font_size", 48)
        color = style.get("color", "#FFFFFF")
        position = style.get("position", "center")
        margin_top = style.get("margin_top", 0)
        margin_bottom = style.get("margin_bottom", 0)
        
        # 计算文本画布大小，为大字体和中文字符预留更多空间
        text_width = min(resolution[0] - 100, resolution[0] * 0.9)  # 留边距
        text_height = max(font_size * 3, font_size + 150)  # 为中文字符预留更多高度
        
        # 创建文本剪辑参数
        text_clip_params = {
            'text': text,
            'font_size': font_size,
            'color': color,
            'size': (int(text_width), int(text_height)),  # 设置画布大小
            'method': 'caption',  # 使用caption方法自动处理文字布局
        }
        
        # 优先使用配置的中文字体
        if self.font_path:
            text_clip_params['font'] = self.font_path
            print(f"🔤 使用中文字体: {Path(self.font_path).name}")
        else:
            print("⚠️  使用系统默认字体")
            # 如果没有指定字体，不添加font参数，让MoviePy使用默认字体
            
        try:
            # 创建文本剪辑
            text_clip = TextClip(**text_clip_params).with_duration(duration)
            
            # 设置位置
            if position == "center":
                if margin_top != 0:
                    # 计算基于中心的偏移位置
                    center_y = resolution[1] // 2
                    actual_y = center_y + margin_top
                    text_clip = text_clip.with_position(("center", actual_y))
                else:
                    text_clip = text_clip.with_position(("center", "center"))
            elif position == "top":
                # 确保顶部有足够空间
                safe_margin_top = max(margin_top, 50)
                text_clip = text_clip.with_position(("center", safe_margin_top))
            elif position == "bottom":
                # 从底部向上计算位置，确保文字完全可见
                bottom_y = resolution[1] - margin_bottom - text_height
                text_clip = text_clip.with_position(("center", max(bottom_y, 50)))
            elif position == "bottom_center":
                bottom_y = resolution[1] - margin_bottom - text_height
                text_clip = text_clip.with_position(("center", max(bottom_y, 50)))
            
            return text_clip
            
        except Exception as e:
            print(f"❌ 文本剪辑创建失败: {e}")
            print(f"   文本内容: {text[:50]}{'...' if len(text) > 50 else ''}")
            print(f"   字体路径: {self.font_path}")
            
            # 回退方案：使用简化参数重试
            try:
                fallback_params = {
                    'text': text,
                    'font_size': font_size,
                    'color': color,
                }
                
                print("🔄 使用回退方案创建文本剪辑...")
                text_clip = TextClip(**fallback_params).with_duration(duration)
                text_clip = text_clip.with_position(("center", "center"))
                return text_clip
                
            except Exception as fallback_error:
                print(f"❌ 回退方案也失败: {fallback_error}")
                # 最后的回退：创建一个空的ColorClip
                return ColorClip(size=(100, 50), color=(255, 0, 0), duration=duration).with_position(("center", "center"))
    
    def _create_base_video(self, segment: SegmentConfig, config: StandardConfig) -> Optional[VideoClip]:
        """创建基础视频"""
        video_source = segment.video_source
        
        if not video_source:
            return None
        
        source_type = video_source.get("type", "color")
        
        if source_type == "global_video" and config.global_video:
            # 使用全局视频
            global_video_url = config.global_video.get("public_url")
            if global_video_url:
                video_path = self.resource_manager.get_resource(global_video_url, "video")
                if video_path:
                    video_clip = VideoFileClip(str(video_path))
                    
                    # 应用全局视频设置
                    start_offset = video_source.get("start_offset", 0)
                    video_clip = video_clip.subclipped(start_offset, start_offset + segment.duration)
                    
                    # 静音处理
                    if config.global_video.get("mute", False):
                        video_clip = video_clip.with_audio(None)
                    
                    # 应用全局视频特效
                    video_clip = self._apply_global_video_effects(video_clip, config.global_video)
                    
                    return video_clip.resized(config.basic.output.resolution)
        
        elif source_type == "color":
            # 颜色背景
            color = video_source.get("color", "#2C3E50")
            color_rgb = self._hex_to_rgb(color)
            return ColorClip(size=config.basic.output.resolution, color=color_rgb, duration=segment.duration)
        
        elif source_type == "image":
            # 图片背景
            image_url = video_source.get("url")
            if image_url:
                image_path = self.resource_manager.get_resource(image_url, "image")
                if image_path:
                    print(f"🖼️  加载图片背景: {image_url}")
                    return ImageClip(str(image_path), duration=segment.duration).resized(config.basic.output.resolution)
                else:
                    print(f"⚠️  图片加载失败: {image_url}")
        
        elif source_type == "video":
            # 自定义视频
            video_url = video_source.get("url")
            if video_url:
                video_path = self.resource_manager.get_resource(video_url, "video")
                if video_path:
                    return VideoFileClip(str(video_path)).subclipped(0, segment.duration).resized(config.basic.output.resolution)
        
        # 默认返回深色背景
        return ColorClip(size=config.basic.output.resolution, color=(44, 62, 80), duration=segment.duration)
    
    def _create_topic_tag(self, topic_text: str, config: StandardConfig) -> Optional[VideoClip]:
        """创建主题标签，优化中文字符支持"""
        if not topic_text:
            return None
        
        # 处理文本兼容性
        topic_text = self._sanitize_text(topic_text)
        
        # 默认样式
        style = {
            "font_size": 100,
            "color": "#FFFFFF",
            "position": "top",
            "margin_top": 100,
            "duration": 2.0,
            "animation": {
                "fade_in": 0.5,
                "fade_out": 0.7
            }
        }
        
        # 优先使用全局topicTitle配置 - 已升级为统一配置系统
        if hasattr(config, 'raw_config'):
            raw_config = config.raw_config
            
            # 1. 先检查全局topicTitle配置
            if 'topicTitle' in raw_config:
                global_topic_config = raw_config['topicTitle']
                if global_topic_config.get('duration'):
                    style['duration'] = global_topic_config['duration']
                if global_topic_config.get('animation'):
                    style['animation'].update(global_topic_config['animation'])
                print(f"🏷️  使用全局topicTitle配置: duration={style['duration']}s")
            
            # 2. 然后检查segment级别的topicTagStyle配置（优先级更高）
            if 'segments' in raw_config and len(raw_config['segments']) > 0:
                segment_config = raw_config['segments'][0]
                if 'topicTagStyle' in segment_config:
                    topic_style = segment_config['topicTagStyle']
                    style.update(topic_style)
                    print(f"🏷️  使用segment级别的topicTag样式: {topic_style}")
        else:
            # 使用默认样式，在统一配置系统中不需要特殊处理
            print("🏷️  使用默认主题标签样式")
        
        # 计算文本画布大小，为中文字符预留更多空间
        font_size = style["font_size"]
        text_width = min(config.basic.output.resolution[0] - 100, config.basic.output.resolution[0] * 0.9)
        text_height = max(font_size * 3, font_size + 150)  # 为中文字符预留更多高度
        
        topic_clip_params = {
            'text': topic_text,
            'font_size': font_size,
            'color': style["color"],
            'size': (int(text_width), int(text_height)),
            'method': 'caption'
        }
        
        # 使用中文字体
        if self.font_path:
            topic_clip_params['font'] = self.font_path
            print(f"🏷️  主题标签使用中文字体: {Path(self.font_path).name}")
        else:
            print("⚠️  主题标签使用系统默认字体")
        
        try:
            # 使用配置中的duration
            duration = style.get("duration", 2.0)
            # 计算安全的margin_top
            safe_margin_top = max(style["margin_top"], 50)
            topic_clip = TextClip(**topic_clip_params).with_duration(duration).with_position(("center", safe_margin_top))
            
            # 使用配置中的动画参数
            animation = style.get("animation", {})
            fade_in_duration = animation.get("fade_in", 0.5)
            fade_out_duration = animation.get("fade_out", 0.7)
            
            # 应用透明度淡入淡出效果，而不是颜色变化的FadeIn/FadeOut
            topic_clip = self._apply_opacity_fade_effects(
                topic_clip, 
                fade_in_duration, 
                fade_out_duration, 
                fade_type="cross"  # 暂时使用cross效果，避免文字看不见的问题
            )
            
            return topic_clip
            
        except Exception as e:
            print(f"❌ 主题标签创建失败: {e}")
            print(f"   标签文本: {topic_text}")
            print(f"   字体路径: {self.font_path}")
            
            # 回退方案：使用简化参数
            try:
                fallback_params = {
                    'text': topic_text,
                    'font_size': font_size,
                    'color': style["color"],
                }
                
                print("🔄 使用回退方案创建主题标签...")
                duration = style.get("duration", 2.0)
                safe_margin_top = max(style["margin_top"], 50)
                topic_clip = TextClip(**fallback_params).with_duration(duration).with_position(("center", safe_margin_top))
                return topic_clip
                
            except Exception as fallback_error:
                print(f"❌ 主题标签回退方案也失败: {fallback_error}")
                return None
    
    def _create_subtitles_from_data(self, subtitles_data: List[Dict[str, Any]], config: StandardConfig) -> List[VideoClip]:
        """
        从字幕数据创建字幕视频片段，支持智能分段显示
        
        Args:
            subtitles_data: 字幕数据列表
            config: 配置对象
            
        Returns:
            字幕视频片段列表
        """
        subtitle_clips = []
        
        for subtitle_data in subtitles_data:
            text = subtitle_data.get("text", "")
            if not text:
                continue
            
            # 处理文本兼容性
            text = self._sanitize_text(text)
            
            # 字幕样式配置
            style = subtitle_data.get("style", {})
            font_size = style.get("font_size", 42)
            color = style.get("color", "white")
            stroke_color = style.get("stroke_color", "black")
            stroke_width = style.get("stroke_width", 2)
            
            # 检查是否需要分段显示
            MAX_LINES = 2
            MAX_CHARS_PER_LINE = 42
            max_chars_per_segment = MAX_CHARS_PER_LINE * MAX_LINES
            
            if len(text) > max_chars_per_segment:
                # 需要分段显示
                print(f"📝 长字幕分段处理: {len(text)}字符 -> 分段显示")
                segments = self._split_text_to_segments(text, MAX_CHARS_PER_LINE, MAX_LINES)
                segment_subtitles = self._create_subtitle_segments(subtitle_data, segments)
                
                # 为每个分段创建字幕片段
                for segment_subtitle in segment_subtitles:
                    segment_clips = self._create_single_subtitle_clip(segment_subtitle, config)
                    subtitle_clips.extend(segment_clips)
            else:
                # 单段显示
                single_clips = self._create_single_subtitle_clip(subtitle_data, config)
                subtitle_clips.extend(single_clips)
        
        return subtitle_clips
    
    def _create_single_subtitle_clip(self, subtitle_data: Dict[str, Any], config: StandardConfig) -> List[VideoClip]:
        """
        创建单个字幕片段
        
        Args:
            subtitle_data: 字幕数据
            config: 配置对象
            
        Returns:
            字幕片段列表（通常只有一个）
        """
        text = subtitle_data.get("text", "")
        if not text:
            return []
        
        # 字幕样式配置
        style = subtitle_data.get("style", {})
        font_size = style.get("font_size", 42)
        color = style.get("color", "white")
        stroke_color = style.get("stroke_color", "black")
        stroke_width = style.get("stroke_width", 2)
        
        # 智能换行处理
        processed_text = self._process_subtitle_text(text, font_size, config.basic.output.resolution)
        
        # 计算字幕画布大小
        text_width = min(config.basic.output.resolution[0] - 100, config.basic.output.resolution[0] * 0.9)
        line_count = len(processed_text.split('\n'))
        line_spacing = font_size * 0.3
        text_height = max(font_size * line_count + line_spacing * (line_count - 1) + 60, font_size * 2 + 100)
        
        # 创建字幕文本参数
        subtitle_clip_params = {
            'text': processed_text,
            'font_size': font_size,
            'color': color,
            'size': (int(text_width), int(text_height)),
            'method': 'caption'
        }
        
        # 添加描边效果
        if stroke_width > 0:
            subtitle_clip_params['stroke_color'] = stroke_color
            subtitle_clip_params['stroke_width'] = stroke_width
        
        # 使用中文字体
        if self.font_path:
            subtitle_clip_params['font'] = self.font_path
        
        # 时间参数
        duration = subtitle_data.get("duration", 3.0)
        start_time = subtitle_data.get("start_time", 0.0)
        
        try:
            subtitle_clip = TextClip(**subtitle_clip_params).with_duration(duration).with_start(start_time)
            
            # 智能位置判断
            optimal_position = self._calculate_optimal_subtitle_position(
                subtitle_data, config, text_height, line_count
            )
            subtitle_clip = subtitle_clip.with_position(optimal_position)
            
            # 应用动画效果
            animation = subtitle_data.get("animation", {})
            if animation:
                animation_type = animation.get("type", "fade_in")
                fade_in_duration = animation.get("fade_in_duration", 0.5)
                fade_out_duration = animation.get("fade_out_duration", 0.3)
                
                if animation_type in ["fade_in", "slide_up"]:
                    subtitle_clip = self._apply_opacity_fade_effects(
                        subtitle_clip, 
                        fade_in_duration=fade_in_duration,
                        fade_out_duration=fade_out_duration,
                        fade_type="cross"
                    )
            
            # 显示分段信息
            segment_info = ""
            if subtitle_data.get("segment_index"):
                segment_info = f" (分段{subtitle_data['segment_index']}/{subtitle_data['total_segments']})"
            
            print(f"📝 字幕片段: {start_time:.2f}s-{start_time + duration:.2f}s ({line_count}行){segment_info} '{text[:30]}'")
            
            return [subtitle_clip]
            
        except Exception as e:
            print(f"❌ 字幕创建失败: {e}")
            print(f"   字幕文本: {text[:50]}")
            print(f"   字体路径: {self.font_path}")
            
            # 回退方案
            try:
                fallback_params = {
                    'text': processed_text,
                    'font_size': font_size,
                    'color': color
                }
                
                print("🔄 使用回退方案创建字幕...")
                subtitle_clip = TextClip(**fallback_params).with_duration(duration).with_start(start_time)
                
                simple_position = self._get_simple_subtitle_position(config, text_height)
                subtitle_clip = subtitle_clip.with_position(simple_position)
                
                print(f"✅ 字幕回退方案成功: {start_time:.2f}s-{start_time + duration:.2f}s")
                return [subtitle_clip]
                
            except Exception as fallback_error:
                print(f"❌ 字幕回退方案也失败: {fallback_error}")
                return []
    
    def _calculate_optimal_subtitle_position(self, subtitle_data: Dict[str, Any], config: StandardConfig, text_height: int, line_count: int) -> tuple:
        """
        智能计算字幕最佳位置，避免与其他元素冲突
        
        Args:
            subtitle_data: 字幕数据
            config: 配置对象
            text_height: 文本高度
            line_count: 行数
            
        Returns:
            位置元组 (x, y) 或 字符串位置
        """
        # 获取视频分辨率
        width, height = config.basic.output.resolution
        
        # 检查是否有用户指定的位置偏好
        position_preference = subtitle_data.get("position", "auto")
        
        if position_preference != "auto":
            # 用户指定位置，使用传统逻辑
            return self._get_traditional_subtitle_position(position_preference, config, text_height)
        
        # 智能位置判断逻辑
        # 1. 首先尝试底部居中（经典字幕位置）
        bottom_margin = 80 + (line_count - 1) * 20  # 根据行数调整底部边距
        bottom_y = height - bottom_margin - text_height
        
        # 2. 检查底部是否有足够空间（避免与界面元素冲突）
        if bottom_y > height * 0.6:  # 底部60%区域可用
            return ("center", max(bottom_y, height * 0.65))
        
        # 3. 如果底部空间不足，尝试上部
        top_margin = 80
        top_y = top_margin
        
        # 4. 检查顶部是否有足够空间
        if top_y + text_height < height * 0.4:  # 顶部40%区域可用
            return ("center", top_y)
        
        # 5. 最后选择安全的中间位置
        middle_y = (height - text_height) // 2
        return ("center", middle_y)
    
    def _get_traditional_subtitle_position(self, position: str, config: StandardConfig, text_height: int) -> tuple:
        """
        传统字幕位置计算
        
        Args:
            position: 位置字符串
            config: 配置对象
            text_height: 文本高度
            
        Returns:
            位置元组
        """
        height = config.basic.output.resolution[1]
        
        if position == "bottom_center":
            return ("center", height - 150 - text_height)
        elif position == "top_center":
            return ("center", 50)
        elif position == "center":
            return "center"
        else:
            # 默认底部居中
            return ("center", height - 150 - text_height)
    
    def _get_simple_subtitle_position(self, config: StandardConfig, text_height: int) -> tuple:
        """
        简化的字幕位置计算（用于回退方案）
        
        Args:
            config: 配置对象
            text_height: 文本高度
            
        Returns:
            位置元组
        """
        height = config.basic.output.resolution[1]
        return ("center", height - 120 - text_height)
    
    def _process_subtitle_text(self, text: str, font_size: int, resolution: List[int]) -> str:
        """
        智能处理字幕文本，使用分段显示而非截断
        
        Args:
            text: 原始文本
            font_size: 字体大小
            resolution: 视频分辨率 [width, height]
            
        Returns:
            处理后的文本（仅用于单段显示的情况）
        """
        # 业界标准配置
        MAX_LINES = 2  # Netflix/YouTube标准：最大2行
        MAX_CHARS_PER_LINE = 42  # Netflix标准：42字符/行
        
        # 针对中英文混合优化字符宽度估算
        english_chars = sum(1 for c in text if c.isascii() and c.isalpha())
        chinese_chars = len(text) - english_chars - sum(1 for c in text if c.isspace() or c in '.,!?;')
        
        # 更精确的字符宽度计算
        avg_char_width = font_size * 0.6  # 英文字符宽度
        if chinese_chars > english_chars:
            avg_char_width = font_size * 0.85  # 中文字符更宽
        
        # 计算实际可用的字符数
        available_width = min(resolution[0] - 200, resolution[0] * 0.85)
        max_chars_per_line = min(int(available_width / avg_char_width), MAX_CHARS_PER_LINE)
        max_chars_per_line = max(max_chars_per_line, 20)  # 最小20字符
        
        print(f"📝 字幕处理: 可用宽度{available_width:.0f}px, 字体{font_size}px, 每行最多{max_chars_per_line}字符")
        
        # 如果文本较短，直接返回
        if len(text) <= max_chars_per_line:
            return text
        
        # 检查是否需要分段显示
        if len(text) > max_chars_per_line * MAX_LINES:
            print(f"📝 文本过长({len(text)}字符)，将使用分段显示")
            # 标记需要分段显示，返回原文本
            return text
        
        # 文本适合2行显示，使用智能换行
        lines = self._intelligent_text_wrap(text, max_chars_per_line, MAX_LINES)
        
        # 应用底部重心原则（金字塔形状）
        if len(lines) == 2:
            lines = self._apply_bottom_heavy_principle(lines)
        
        result = '\n'.join(lines)
        print(f"📝 换行结果: {len(lines)}行, 内容: {repr(result[:50])}")
        
        return result
    
    def _intelligent_text_wrap(self, text: str, max_chars_per_line: int, max_lines: int) -> List[str]:
        """
        智能字幕换行算法，无截断，遵循Netflix/YouTube标准
        
        Args:
            text: 要换行的文本
            max_chars_per_line: 每行最大字符数
            max_lines: 最大行数（通常为2）
            
        Returns:
            换行后的文本行列表
        """
        # 清理文本，移除多余空格
        text = ' '.join(text.split())
        
        # 如果文本可以放在一行，直接返回
        if len(text) <= max_chars_per_line:
            return [text]
        
        # 如果只能有一行，直接返回（不截断）
        if max_lines == 1:
            return [text]
        
        # 两行处理：寻找最佳分割点
        best_split = self._find_optimal_split_point(text, max_chars_per_line)
        
        if best_split == -1:
            # 没有找到合适的分割点，在中间位置强制分割
            mid_point = len(text) // 2
            # 尝试找到最近的空格
            for i in range(mid_point, min(mid_point + 20, len(text))):
                if text[i] == ' ':
                    best_split = i + 1
                    break
            
            if best_split == -1:
                # 仍然没有找到，使用中间位置
                best_split = mid_point
        
        # 使用最佳分割点
        first_line = text[:best_split].strip()
        second_line = text[best_split:].strip()
        
        return [first_line, second_line]
    
    def _split_text_to_segments(self, text: str, max_chars_per_line: int, max_lines: int) -> List[str]:
        """
        智能分割长文本为多个语义完整的段落
        
        Args:
            text: 要分割的文本
            max_chars_per_line: 每行最大字符数
            max_lines: 每段最大行数
            
        Returns:
            分割后的文本段落列表
        """
        # 清理文本
        text = ' '.join(text.split())
        
        # 计算每段的最大字符数
        max_chars_per_segment = max_chars_per_line * max_lines
        
        # 如果文本不需要分段，直接返回
        if len(text) <= max_chars_per_segment:
            return [text]
        
        print(f"📝 开始分段: 原文本{len(text)}字符，每段最多{max_chars_per_segment}字符")
        
        segments = []
        remaining_text = text
        
        while remaining_text:
            if len(remaining_text) <= max_chars_per_segment:
                # 剩余文本可以放在一段内
                segments.append(remaining_text)
                break
            
            # 寻找最佳分割点
            best_split = self._find_semantic_split_point(remaining_text, max_chars_per_segment)
            
            if best_split == -1:
                # 没有找到合适的分割点，使用默认分割
                best_split = max_chars_per_segment
                # 向后查找空格，避免在单词中间断开
                for i in range(best_split, min(best_split + 20, len(remaining_text))):
                    if remaining_text[i] == ' ':
                        best_split = i
                        break
            
            # 分割文本
            segment = remaining_text[:best_split].strip()
            segments.append(segment)
            remaining_text = remaining_text[best_split:].strip()
            
            print(f"📝 分段 {len(segments)}: {len(segment)}字符 - '{segment[:30]}{'...' if len(segment) > 30 else ''}'")
        
        print(f"📝 分段完成: 共{len(segments)}段")
        return segments
    
    def _find_semantic_split_point(self, text: str, max_chars: int) -> int:
        """
        寻找语义分割点，优先在句子或逻辑单元结束处分割
        
        Args:
            text: 要分割的文本
            max_chars: 最大字符数
            
        Returns:
            最佳分割点位置，-1表示未找到
        """
        # 搜索范围：最大字符数的70%-100%
        search_start = max(max_chars * 7 // 10, 20)
        search_end = min(max_chars, len(text))
        
        # 定义语义分割点的优先级
        semantic_breaks = [
            # 1. 句子结束符后（最高优先级）
            ['. ', '! ', '? ', '; ', '。 ', '！ ', '？ ', '； '],
            # 2. 段落或思想单元结束
            ['\n', '，而且', '，但是', '，然而', '，因此', '，所以'],
            # 3. 连接词前
            [' 而且 ', ' 但是 ', ' 然而 ', ' 因此 ', ' 所以 ', ' 同时 ', ' 另外 '],
            # 4. 逗号后
            [', ', '， ', '、 '],
            # 5. 任何空格
            [' ']
        ]
        
        best_split = -1
        best_priority = float('inf')
        
        # 按优先级查找分割点
        for priority_level, break_patterns in enumerate(semantic_breaks):
            for pattern in break_patterns:
                # 在搜索范围内查找
                for i in range(search_start, search_end):
                    if i + len(pattern) <= len(text):
                        if text[i:i+len(pattern)] == pattern:
                            # 计算距离目标位置的偏差
                            distance_penalty = abs(i - max_chars * 0.85)
                            score = priority_level * 100 + distance_penalty
                            
                            if score < best_priority:
                                best_priority = score
                                best_split = i + len(pattern)
        
        return best_split
    
    def _create_subtitle_segments(self, subtitle_data: Dict[str, Any], segments: List[str]) -> List[Dict[str, Any]]:
        """
        为每个文本段落创建字幕数据
        
        Args:
            subtitle_data: 原始字幕数据
            segments: 分割后的文本段落列表
            
        Returns:
            分段字幕数据列表
        """
        if len(segments) <= 1:
            return [subtitle_data]
        
        # 计算每段的时长
        total_duration = subtitle_data.get("duration", 3.0)
        start_time = subtitle_data.get("start_time", 0.0)
        
        # 根据文本长度按比例分配时长
        total_chars = sum(len(segment) for segment in segments)
        segment_subtitles = []
        
        current_time = start_time
        for i, segment in enumerate(segments):
            # 按字符数比例分配时长
            char_ratio = len(segment) / total_chars
            segment_duration = total_duration * char_ratio
            
            # 确保每段至少有1.5秒显示时间
            segment_duration = max(segment_duration, 1.5)
            
            # 如果是最后一段，调整时长以确保总时长正确
            if i == len(segments) - 1:
                segment_duration = start_time + total_duration - current_time
            
            # 创建分段字幕数据
            segment_subtitle = {
                "text": segment,
                "start_time": current_time,
                "duration": segment_duration,
                "position": subtitle_data.get("position", "auto"),
                "style": subtitle_data.get("style", {}),
                "animation": subtitle_data.get("animation", {}),
                "segment_index": i + 1,
                "total_segments": len(segments)
            }
            
            segment_subtitles.append(segment_subtitle)
            current_time += segment_duration
            
            print(f"📝 分段字幕 {i+1}/{len(segments)}: {current_time-segment_duration:.2f}s-{current_time:.2f}s ({len(segment)}字符)")
        
        return segment_subtitles
    
    def _find_optimal_split_point(self, text: str, max_chars_per_line: int) -> int:
        """
        寻找最佳分割点，遵循业界标准换行规则
        
        Args:
            text: 文本
            max_chars_per_line: 每行最大字符数
            
        Returns:
            最佳分割点位置，-1表示未找到
        """
        # 目标分割位置：第一行占总长度的 40-60%
        target_first_line = len(text) // 2
        search_start = max(20, target_first_line - 15)  # 搜索范围
        search_end = min(max_chars_per_line, target_first_line + 15)
        
        # 定义断点优先级（遵循Netflix标准）
        priority_breaks = [
            # 1. 句子结束符后
            ['.', '!', '?', ';', '。', '！', '？', '；'],
            # 2. 逗号后
            [',', '，', '、'],
            # 3. 连词前
            [' and ', ' but ', ' or ', ' so ', ' yet ', ' for ', ' nor '],
            # 4. 介词前
            [' in ', ' on ', ' at ', ' by ', ' for ', ' with ', ' from '],
            # 5. 任何空格
            [' ']
        ]
        
        best_split = -1
        best_score = float('inf')
        
        # 按优先级查找断点
        for priority_level, break_chars in enumerate(priority_breaks):
            for break_char in break_chars:
                # 在搜索范围内查找
                for i in range(search_start, search_end):
                    if i + len(break_char) <= len(text):
                        if text[i:i+len(break_char)] == break_char:
                            # 计算分割质量得分
                            score = self._calculate_split_score(text, i + len(break_char), target_first_line, priority_level)
                            if score < best_score:
                                best_score = score
                                best_split = i + len(break_char)
        
        return best_split
    
    def _calculate_split_score(self, text: str, split_pos: int, target_pos: int, priority_level: int) -> float:
        """
        计算分割点的质量得分（越低越好）
        
        Args:
            text: 原始文本
            split_pos: 分割位置
            target_pos: 目标位置
            priority_level: 优先级等级
            
        Returns:
            质量得分
        """
        # 位置偏差惩罚
        position_penalty = abs(split_pos - target_pos) * 0.1
        
        # 优先级惩罚
        priority_penalty = priority_level * 10
        
        # 行长度平衡奖励
        first_line_len = split_pos
        second_line_len = len(text) - split_pos
        balance_bonus = -abs(first_line_len - second_line_len) * 0.05
        
        # 避免过短行的惩罚
        if first_line_len < 10 or second_line_len < 10:
            short_line_penalty = 50
        else:
            short_line_penalty = 0
        
        return position_penalty + priority_penalty + balance_bonus + short_line_penalty
    
    def _apply_bottom_heavy_principle(self, lines: List[str]) -> List[str]:
        """
        应用底部重心原则，确保下行比上行长（金字塔形状）
        
        Args:
            lines: 原始行列表
            
        Returns:
            调整后的行列表
        """
        if len(lines) != 2:
            return lines
        
        first_line, second_line = lines
        
        # 如果第一行明显长于第二行，尝试重新分配
        if len(first_line) > len(second_line) + 10:
            # 尝试将第一行的部分内容移到第二行
            words = first_line.split()
            if len(words) > 2:
                # 移动最后一个或两个单词到第二行
                move_words = 1 if len(words) <= 4 else 2
                new_first_line = ' '.join(words[:-move_words])
                new_second_line = ' '.join(words[-move_words:]) + ' ' + second_line
                
                # 检查新分配是否更好
                if (len(new_first_line) >= 10 and 
                    len(new_second_line) <= 42 and
                    len(new_second_line) >= len(new_first_line) - 5):
                    return [new_first_line, new_second_line]
        
        return lines
    
    def _apply_effects(self, clip: VideoClip, effects: Dict[str, Any]) -> VideoClip:
        """应用视频效果"""
        text_animation = effects.get("text_animation", "none")
        transition_out = effects.get("transition_out", "none")
        
        if text_animation == "fade_in":
            clip = clip.with_effects([vfx.FadeIn(0.5)])
        elif text_animation == "slide_up":
            clip = clip.with_effects([vfx.FadeIn(0.5)])
        
        if transition_out == "fade":
            clip = clip.with_effects([vfx.FadeOut(0.5)])
        
        return clip
    
    def _apply_segment_effects(self, clip: VideoClip, effects: Dict[str, Any]) -> VideoClip:
        """应用片段特效"""
        zoom = effects.get("zoom", 1.0)
        if zoom != 1.0:
            clip = clip.resized(zoom)
        
        # 其他效果可以在这里添加
        return clip
    
    def render_audio_track(self, config: StandardConfig) -> Optional[AudioClip]:
        """渲染音频轨道"""
        print("🎵 渲染音频轨道...")
        
        audio_clips = []
        current_time = 0
        
        try:
            # 添加片头音频（如果有）
            if config.intro:
                intro_duration = config.intro.duration
                current_time += intro_duration
            
            # 添加片段音频
            for segment in config.segments:
                segment_start_time = current_time
                narration_time = segment_start_time  # 片段内语音时间累积
                
                # 添加场景音频
                for scene in segment.scenes:
                    audio_url = scene.audio.url
                    if audio_url:
                        audio_path = self.resource_manager.get_resource(audio_url, "audio")
                        if audio_path:
                            try:
                                scene_audio = AudioFileClip(str(audio_path))
                                
                                # 调整音频时长
                                adjusted_duration = scene.audio.duration
                                if adjusted_duration != scene_audio.duration:
                                    scene_audio = scene_audio.with_duration(adjusted_duration)
                                
                                # 设置开始时间
                                scene_audio = scene_audio.with_start(narration_time)
                                audio_clips.append(scene_audio)
                                
                                print(f"🎙️  添加语音: {narration_time:.2f}s-{narration_time + scene_audio.duration:.2f}s")
                                
                                # 更新下一个语音的开始时间
                                narration_time += scene_audio.duration
                                
                            except Exception as e:
                                print(f"⚠️  语音加载失败: {e}")
                
                # 更新总时间（使用场景时长之和）
                segment_duration = sum(scene.audio.duration for scene in segment.scenes)
                current_time += segment_duration
            
            # 添加片尾音频（如果有）
            if config.outro:
                outro_duration = config.outro.duration
                current_time += outro_duration
            
            # 添加背景音乐
            if config.basic.audio.background and config.basic.audio.background.get('music'):
                bg_music_url = config.basic.audio.background['music']
                bg_music_path = self.resource_manager.get_resource(bg_music_url, "audio")
                if bg_music_path:
                    try:
                        bg_music = AudioFileClip(str(bg_music_path))
                        
                        # 调整背景音乐长度
                        total_duration = current_time
                        if bg_music.duration < total_duration:
                            # 如果背景音乐较短，循环播放
                            loops_needed = int(total_duration / bg_music.duration) + 1
                            # 使用concatenate_audioclips来循环音频
                            from moviepy import concatenate_audioclips
                            bg_music = concatenate_audioclips([bg_music] * loops_needed)

                        bg_music = bg_music.subclipped(0, total_duration)
                        
                        # 应用背景音乐音量
                        bg_volume = config.basic.audio.background.get('volume', 0.3)
                        if bg_volume != 1.0:
                            bg_music = bg_music.with_volume_scaled(bg_volume)
                        
                        audio_clips.append(bg_music)
                        print(f"🎶 添加背景音乐: {total_duration:.2f}s")
                    except Exception as e:
                        print(f"⚠️  背景音乐加载失败: {e}")
            
            # 合成音频轨道
            if audio_clips:
                final_audio = CompositeAudioClip(audio_clips)
                print(f"✅ 音频轨道渲染完成，包含 {len(audio_clips)} 个音频片段")
                return final_audio
            else:
                print("⚠️  没有找到音频内容")
                return None
                
        except Exception as e:
            print(f"❌ 音频轨道渲染失败: {e}")
            return None
    
    def _sanitize_text(self, text: str) -> str:
        """处理文本中的特殊字符兼容性，优化中文支持"""
        if not text:
            return text
        
        # 确保文本是字符串类型
        if not isinstance(text, str):
            text = str(text)
        
        # 处理常见的特殊字符兼容性问题，保留中文标点符号
        replacements = {
            # 英文标点替换，但保留中文标点
            '•': '·',  # 将bullet point替换为更兼容的中点
            '–': '-',  # en dash替换为普通连字符  
            '—': '—',  # em dash保留（中文常用）
            '"': '"',  # 左双引号替换为中文引号
            '"': '"',  # 右双引号替换为中文引号
            ''': "'",  # 左单引号
            ''': "'",  # 右单引号
            '…': '…',  # 省略号保留（中文常用）
        }
        
        for original, replacement in replacements.items():
            text = text.replace(original, replacement)
        
        # 确保文本编码正确
        try:
            # 检查是否包含中文字符
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
            if has_chinese:
                print(f"📝 检测到中文内容: {text[:20]}{'...' if len(text) > 20 else ''}")
            
            # 确保文本是有效的UTF-8编码
            encoded = text.encode('utf-8')
            decoded = encoded.decode('utf-8')
            
            # 验证编码解码是否成功
            if decoded != text:
                print("⚠️  文本编码不一致，进行修复")
                text = decoded
                
        except UnicodeEncodeError as e:
            print(f"⚠️  文本编码错误: {e}")
            # 如果编码失败，使用更温和的处理方式
            try:
                # 先尝试忽略错误字符
                text = text.encode('utf-8', errors='ignore').decode('utf-8')
                print("✅ 文本编码问题已修复")
            except Exception as fallback_error:
                print(f"❌ 文本编码修复失败: {fallback_error}")
                # 最后的回退方案：只保留ASCII字符
                text = ''.join(char for char in text if ord(char) < 128)
                print("⚠️  已回退到ASCII字符")
        except UnicodeDecodeError as e:
            print(f"⚠️  文本解码错误: {e}")
            text = str(text)  # 强制转换为字符串
        
        return text

    def _hex_to_rgb(self, hex_color: str) -> tuple:
        """将十六进制颜色转换为RGB元组"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def get_total_duration(self, config: StandardConfig) -> float:
        """计算总时长"""
        total_duration = 0
        
        # 片头时长
        if config.intro:
            total_duration += config.intro.duration
        
        # 片段时长（所有场景的音频时长之和）
        for segment in config.segments:
            segment_duration = sum(scene.audio.duration for scene in segment.scenes)
            total_duration += segment_duration
        
        # 片尾时长
        if config.outro:
            total_duration += config.outro.duration
        
        return total_duration
    
    def _apply_global_video_effects(self, clip: VideoClip, global_video_config: Dict[str, Any]) -> VideoClip:
        """应用全局视频特效"""
        effects_config = global_video_config.get("effects", {})
        
        if not effects_config.get("enabled", True):
            return clip
        
        effects_to_apply = []
        
        # 亮度调整
        brightness = effects_config.get("brightness", 1.0)
        if brightness != 1.0:
            effects_to_apply.append(vfx.MultiplyColor(factor=brightness))
        
        # 对比度调整
        contrast = effects_config.get("contrast", 1.0)
        if contrast != 1.0:
            effects_to_apply.append(vfx.LumContrast(contrast=contrast))
        
        # 饱和度调整 - 使用颜色倍增近似
        saturation = effects_config.get("saturation", 1.0)
        if saturation != 1.0:
            # 饱和度效果通过颜色调整实现
            sat_factor = (saturation, saturation, saturation)
            effects_to_apply.append(vfx.MultiplyColor(factor=sat_factor))
        
        # 模糊效果 - 使用自定义模糊滤镜
        blur = effects_config.get("blur", 0)
        if blur > 0:
            effects_to_apply.append(self._create_blur_effect(blur))
        
        # 应用所有特效
        if effects_to_apply:
            clip = clip.with_effects(effects_to_apply)
        
        return clip
    
    def _create_blur_effect(self, blur_radius: float):
        """创建模糊效果"""
        # 使用自定义变换实现模糊效果
        def blur_frame(frame):
            try:
                from scipy.ndimage import gaussian_filter
                # 对每个颜色通道应用高斯模糊
                blurred_frame = frame.copy()
                for i in range(3):  # RGB三个通道
                    blurred_frame[:, :, i] = gaussian_filter(frame[:, :, i], sigma=blur_radius)
                return blurred_frame
            except ImportError:
                # 如果没有scipy，返回原帧
                return frame
        
        return vfx.MultiplyColor(factor=1.0)  # 占位符，实际模糊需要scipy
    
    def _apply_video_effects(self, clip: VideoClip, video_effects: Dict[str, Any]) -> VideoClip:
        """应用片段视频特效"""
        if not video_effects:
            return clip
        
        effects_to_apply = []
        
        # 缩放效果
        zoom = video_effects.get("zoom", 1.0)
        if zoom != 1.0:
            effects_to_apply.append(vfx.Resize(zoom))
        
        # 旋转效果
        rotate = video_effects.get("rotate", 0)
        if rotate != 0:
            effects_to_apply.append(vfx.Rotate(angle=rotate))
        
        # 翻转效果
        flip = video_effects.get("flip", {})
        if flip.get("horizontal", False):
            effects_to_apply.append(vfx.MirrorX())
        if flip.get("vertical", False):
            effects_to_apply.append(vfx.MirrorY())
        
        # 平移效果 - 使用position调整
        pan = video_effects.get("pan", {})
        pan_x = pan.get("x", 0)
        pan_y = pan.get("y", 0)
        if pan_x != 0 or pan_y != 0:
            # 平移效果通过margin实现
            effects_to_apply.append(vfx.Margin(mar=(pan_x, pan_y, 0, 0)))
        
        # 应用所有特效
        if effects_to_apply:
            clip = clip.with_effects(effects_to_apply)
        
        return clip
    
    def _apply_transitions(self, clip: VideoClip, transitions: Dict[str, Any]) -> VideoClip:
        """应用转场效果"""
        if not transitions:
            return clip
        
        effects_to_apply = []
        
        # 淡入效果
        fade_in = transitions.get("fade_in", {})
        if fade_in.get("type") == "fade":
            duration = fade_in.get("duration", 1.0)
            effects_to_apply.append(vfx.FadeIn(duration))
        
        # 淡出效果
        fade_out = transitions.get("fade_out", {})
        if fade_out.get("type") == "fade":
            duration = fade_out.get("duration", 1.0)
            effects_to_apply.append(vfx.FadeOut(duration))
        
        # 其他转场效果
        cross_fade_in = transitions.get("cross_fade_in", {})
        if cross_fade_in.get("type") == "cross_fade":
            duration = cross_fade_in.get("duration", 1.0)
            effects_to_apply.append(vfx.CrossFadeIn(duration))
        
        cross_fade_out = transitions.get("cross_fade_out", {})
        if cross_fade_out.get("type") == "cross_fade":
            duration = cross_fade_out.get("duration", 1.0)
            effects_to_apply.append(vfx.CrossFadeOut(duration))
        
        # 应用所有转场效果
        if effects_to_apply:
            clip = clip.with_effects(effects_to_apply)
        
        return clip
    
    def _apply_intro_outro_effects(self, clip: VideoClip, effects: Dict[str, Any]) -> VideoClip:
        """应用片头片尾特效"""
        if not effects:
            return clip
        
        effects_to_apply = []
        
        # 文本动画效果
        text_animation = effects.get("text_animation", "none")
        if text_animation == "fade_in":
            effects_to_apply.append(vfx.FadeIn(0.5))
        elif text_animation == "slide_up":
            effects_to_apply.append(vfx.SlideIn(duration=0.5, side='bottom'))
        elif text_animation == "zoom_in":
            # 使用缩放效果模拟zoom_in
            effects_to_apply.append(vfx.FadeIn(0.5))
        
        # 转场效果
        transition_out = effects.get("transition_out", "none")
        if transition_out == "fade":
            effects_to_apply.append(vfx.FadeOut(0.5))
        elif transition_out == "slide_out":
            effects_to_apply.append(vfx.SlideOut(duration=0.5, side='top'))
        
        transition_in = effects.get("transition_in", "none")
        if transition_in == "fade":
            effects_to_apply.append(vfx.FadeIn(0.5))
        elif transition_in == "slide_in":
            effects_to_apply.append(vfx.SlideIn(duration=0.5, side='bottom'))
        
        # 应用所有特效
        if effects_to_apply:
            clip = clip.with_effects(effects_to_apply)
        
        return clip
    
    def _apply_opacity_fade_effects(self, clip: VideoClip, fade_in_duration: float = 0.0, fade_out_duration: float = 0.0, fade_type: str = "cross"):
        """
        应用透明度淡入淡出效果
        
        参数:
        - fade_in_duration: 淡入时长
        - fade_out_duration: 淡出时长
        - fade_type: 效果类型
          - "none": 无效果（直接显示/隐藏）
          - "cross": 使用CrossFadeIn/CrossFadeOut（推荐，轻微颜色变化）⭐
          - "simple": 使用FadeIn/FadeOut（标准效果，可能有颜色变化）
        """
        if fade_in_duration <= 0 and fade_out_duration <= 0:
            return clip
        
        if fade_type == "none":
            # 无渐变效果，直接显示/隐藏
            return clip
        
        elif fade_type == "cross":
            # 使用CrossFadeIn/CrossFadeOut（推荐选项）
            effects_to_apply = []
            if fade_in_duration > 0:
                effects_to_apply.append(vfx.CrossFadeIn(fade_in_duration))
            if fade_out_duration > 0:
                effects_to_apply.append(vfx.CrossFadeOut(fade_out_duration))
            
            if effects_to_apply:
                return clip.with_effects(effects_to_apply)
            else:
                return clip
        
        elif fade_type == "simple":
            # 使用标准FadeIn/FadeOut
            effects_to_apply = []
            if fade_in_duration > 0:
                effects_to_apply.append(vfx.FadeIn(fade_in_duration))
            if fade_out_duration > 0:
                effects_to_apply.append(vfx.FadeOut(fade_out_duration))
            
            if effects_to_apply:
                return clip.with_effects(effects_to_apply)
            else:
                return clip
        
        else:
            # 默认使用cross效果
            return self._apply_opacity_fade_effects(clip, fade_in_duration, fade_out_duration, "cross")
    
    def _create_scene_background(self, background_config: SceneBackgroundConfig, resolution: List[int], duration: float) -> VideoClip:
        """创建场景背景视频"""
        try:
            background_clip = None
            
            if background_config.type == "color":
                color = background_config.value or "#2C3E50"
                color_rgb = self._hex_to_rgb(color)
                background_clip = ColorClip(size=resolution, color=color_rgb, duration=duration)
            
            elif background_config.type == "image":
                if background_config.url:
                    image_path = self.resource_manager.get_resource(background_config.url, "image")
                    if image_path:
                        background_clip = ImageClip(str(image_path), duration=duration).resized(resolution)
            
            elif background_config.type == "video":
                if background_config.url:
                    video_path = self.resource_manager.get_resource(background_config.url, "video")
                    if video_path:
                        background_clip = VideoFileClip(str(video_path)).subclipped(0, duration).resized(resolution)
            
            # 如果没有成功创建背景，使用默认背景
            if background_clip is None:
                background_clip = ColorClip(size=resolution, color=(44, 62, 80), duration=duration)
            
            # 应用背景动画效果
            if background_config.animation:
                print(f"🎬 应用背景动画效果: {background_config.animation}")
                # 优先使用增强动画渲染器
                if ENHANCED_ANIMATION_AVAILABLE and ("preset" in background_config.animation):
                    background_clip = enhanced_animation_renderer.apply_enhanced_background_animation(
                        background_clip, background_config.animation
                    )
                else:
                    # 回退到原有动画系统
                    background_clip = self._apply_background_animation(background_clip, background_config.animation)
            
            return background_clip
            
        except Exception as e:
            print(f"⚠️  创建场景背景失败: {e}")
            return ColorClip(size=resolution, color=(44, 62, 80), duration=duration)
    
    def _apply_background_animation(self, background_clip: VideoClip, animation_config: Dict[str, Any]) -> VideoClip:
        """应用背景动画效果 - 使用MoviePy动态API实现真正的动画"""
        try:
            duration = background_clip.duration
            effects = []
            
            # 缩放动画效果
            zoom = animation_config.get("zoom", 1.0)
            if zoom != 1.0:
                print(f"  🔍 应用动态缩放动画: 1.0 → {zoom}")
                
                def zoom_animation(t):
                    """动态缩放函数"""
                    progress = t / duration
                    ease_progress = 0.5 * (1 - np.cos(progress * np.pi))
                    current_zoom = 1.0 + (zoom - 1.0) * ease_progress
                    return current_zoom
                
                effects.append(vfx.Resize(zoom_animation))
                print(f"    ✅ 动态缩放: {zoom_animation(0):.2f} → {zoom_animation(duration):.2f}")
            
            # 亮度动画效果
            brightness = animation_config.get("brightness", 1.0)
            if brightness != 1.0:
                print(f"  💡 应用动态亮度动画: 1.0 → {brightness}")
                
                def brightness_filter(get_frame, t):
                    """动态亮度滤镜"""
                    frame = get_frame(t)
                    progress = t / duration
                    ease_progress = 0.5 * (1 - np.cos(progress * np.pi))
                    current_brightness = 1.0 + (brightness - 1.0) * ease_progress
                    # 应用亮度
                    bright_frame = frame * current_brightness
                    # 确保像素值在0-255范围内
                    bright_frame = np.clip(bright_frame, 0, 255)
                    return bright_frame.astype('uint8')
                
                background_clip = background_clip.transform(brightness_filter)
                print(f"    ✅ 动态亮度已应用")
            
            # 旋转动画效果
            rotate = animation_config.get("rotate", 0)
            if rotate != 0:
                print(f"  🔄 应用动态旋转动画: 0° → {rotate}°")
                
                def rotate_animation(t):
                    """动态旋转函数"""
                    progress = t / duration
                    ease_progress = 0.5 * (1 - np.cos(progress * np.pi))
                    current_rotate = rotate * ease_progress
                    return current_rotate
                
                effects.append(vfx.Rotate(angle=rotate_animation))
                print(f"    ✅ 动态旋转: {rotate_animation(0):.1f}° → {rotate_animation(duration):.1f}°")
            
            # 对比度动画效果
            contrast = animation_config.get("contrast", 1.0)
            if contrast != 1.0:
                print(f"  🎨 应用动态对比度动画: 1.0 → {contrast}")
                
                def contrast_animation(t):
                    """动态对比度函数"""
                    progress = t / duration
                    ease_progress = 0.5 * (1 - np.cos(progress * np.pi))
                    current_contrast = 1.0 + (contrast - 1.0) * ease_progress
                    return current_contrast
                
                effects.append(vfx.LumContrast(contrast=contrast_animation))
                print(f"    ✅ 动态对比度: {contrast_animation(0):.2f} → {contrast_animation(duration):.2f}")
            
            # 饱和度动画效果
            saturation = animation_config.get("saturation", 1.0)
            if saturation != 1.0:
                print(f"  🌈 应用动态饱和度动画: 1.0 → {saturation}")
                
                def saturation_filter(get_frame, t):
                    """动态饱和度滤镜"""
                    frame = get_frame(t)
                    progress = t / duration
                    ease_progress = 0.5 * (1 - np.cos(progress * np.pi))
                    current_saturation = 1.0 + (saturation - 1.0) * ease_progress
                    
                    # 转换为HSV
                    hsv = cv2.cvtColor(frame, cv2.COLOR_RGB2HSV).astype(np.float32)
                    # 调整饱和度通道
                    hsv[:, :, 1] = hsv[:, :, 1] * current_saturation
                    # 确保值在0-255范围内
                    hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 255)
                    # 转回RGB
                    rgb = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2RGB)
                    return rgb
                
                background_clip = background_clip.transform(saturation_filter)
                print(f"    ✅ 动态饱和度已应用")
            
            # 平移动画效果
            pan = animation_config.get("pan", {})
            if pan:
                pan_x = pan.get("x", 0)
                pan_y = pan.get("y", 0)
                if pan_x != 0 or pan_y != 0:
                    print(f"  🔄 应用动态平移动画: (0,0) → ({pan_x},{pan_y})")
                    
                    def pan_animation(t):
                        """动态平移函数"""
                        progress = t / duration
                        ease_progress = 0.5 * (1 - np.cos(progress * np.pi))
                        current_x = pan_x * ease_progress
                        current_y = pan_y * ease_progress
                        return (current_x, current_y)
                    
                    background_clip = background_clip.with_position(pan_animation)
                    print(f"    ✅ 动态平移: ({pan_animation(0)[0]:.1f},{pan_animation(0)[1]:.1f}) → ({pan_animation(duration)[0]:.1f},{pan_animation(duration)[1]:.1f})")
            
            # 应用所有动画效果
            if effects:
                background_clip = background_clip.with_effects(effects)
                print(f"  ✅ 应用了 {len(effects)} 个动态动画效果")
            
            return background_clip
            
        except Exception as e:
            print(f"⚠️  应用背景动画失败: {e}")
            return background_clip





 