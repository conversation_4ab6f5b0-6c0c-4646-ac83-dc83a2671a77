#!/usr/bin/env python3
"""
生成测试图片
"""

import os
import numpy as np
import cv2

def generate_test_image(width, height, pattern_type, filename):
    """生成测试图片"""
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    if pattern_type == "gradient":
        # 创建渐变
        x = np.linspace(0, 255, width)
        y = np.linspace(0, 255, height)
        X, Y = np.meshgrid(x, y)
        image[:, :, 0] = X  # 红色渐变
        image[:, :, 1] = Y  # 绿色渐变
        
    elif pattern_type == "circles":
        # 创建同心圆
        center = (width // 2, height // 2)
        for r in range(0, min(width, height) // 2, 20):
            cv2.circle(image, center, r, (r % 255, (r + 85) % 255, (r + 170) % 255), 2)
            
    elif pattern_type == "grid":
        # 创建网格
        for x in range(0, width, 50):
            cv2.line(image, (x, 0), (x, height), (255, 255, 255), 1)
        for y in range(0, height, 50):
            cv2.line(image, (0, y), (width, y), (255, 255, 255), 1)
            
    elif pattern_type == "text":
        # 创建文字图案
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(image, "Test Pattern", (width//4, height//2), font, 2, (255, 255, 255), 2)
            
    elif pattern_type == "shapes":
        # 创建各种形状
        cv2.rectangle(image, (50, 50), (200, 200), (255, 0, 0), -1)
        cv2.circle(image, (400, 300), 100, (0, 255, 0), -1)
        cv2.triangle = np.array([[600, 100], [500, 400], [700, 400]], np.int32)
        cv2.fillPoly(image, [cv2.triangle], (0, 0, 255))
    
    # 保存图片
    cv2.imwrite(filename, image)
    print(f"✅ 生成测试图片: {filename}")

def main():
    """主函数"""
    # 确保目录存在
    os.makedirs("test_images", exist_ok=True)
    
    # 生成不同类型的测试图片
    patterns = ["gradient", "circles", "grid", "text", "shapes"]
    for i, pattern in enumerate(patterns, 1):
        filename = f"test_images/test{i}.jpg"
        generate_test_image(800, 600, pattern, filename)
    
    print("✅ 所有测试图片生成完成")

if __name__ == "__main__":
    main() 