"""
🎬 Enhanced Animation Renderer - 集成专业动画库的渲染器
Enhanced Animation Renderer with Professional Animation Library Integration

集成animation_library.py的专业动画效果到现有的template_renderer.py中
"""

from typing import Dict, Any, Callable, List
import numpy as np
from moviepy import VideoClip, vfx
from animation_library import animation_library, AnimationConfig

class EnhancedAnimationRenderer:
    """增强的动画渲染器 - 集成专业动画库"""
    
    def __init__(self):
        self.animation_lib = animation_library
    
    def apply_enhanced_background_animation(self, background_clip: VideoClip, animation_config: Dict[str, Any]) -> VideoClip:
        """应用增强的背景动画效果"""
        try:
            duration = background_clip.duration
            
            # 检查是否使用预设动画
            if "preset" in animation_config:
                return self._apply_preset_animation(background_clip, animation_config, duration)
            else:
                # 使用传统的自定义动画
                return self._apply_custom_animation(background_clip, animation_config, duration)
                
        except Exception as e:
            print(f"⚠️  应用增强动画效果失败: {e}")
            return background_clip
    
    def _apply_preset_animation(self, background_clip: VideoClip, config: Dict[str, Any], duration: float) -> VideoClip:
        """应用预设动画效果"""
        preset_name = config.get("preset", "ken_burns")
        intensity = config.get("intensity", 1.0)
        scene_type = config.get("scene_type", "general")
        
        print(f"🎬 应用预设动画: {preset_name} (强度: {intensity}, 场景: {scene_type})")
        
        try:
            # 获取动画函数
            animation_functions = self.animation_lib.get_animation_functions(preset_name, duration, intensity)
            
            # 应用动画效果
            return self._apply_animation_functions(background_clip, animation_functions, duration)
            
        except ValueError as e:
            print(f"⚠️  预设动画 '{preset_name}' 不存在，使用默认动画")
            # 回退到推荐动画
            recommended = self.animation_lib.suggest_animation_for_scene(scene_type, duration)
            animation_functions = self.animation_lib.get_animation_functions(recommended, duration, intensity)
            return self._apply_animation_functions(background_clip, animation_functions, duration)
    
    def _apply_custom_animation(self, background_clip: VideoClip, config: Dict[str, Any], duration: float) -> VideoClip:
        """应用自定义动画效果（兼容原有配置）"""
        print(f"🎨 应用自定义动画效果")
        
        effects = []
        
        # 缩放动画
        zoom = config.get("zoom", 1.0)
        if zoom != 1.0:
            def zoom_animation(t):
                progress = t / duration
                ease_progress = 0.5 * (1 - np.cos(progress * np.pi))
                current_zoom = 1.0 + (zoom - 1.0) * ease_progress
                return current_zoom
            effects.append(vfx.Resize(zoom_animation))
        
        # 平移动画
        pan = config.get("pan", {})
        if pan:
            pan_x = pan.get("x", 0)
            pan_y = pan.get("y", 0)
            if pan_x != 0 or pan_y != 0:
                def pan_animation(t):
                    progress = t / duration
                    ease_progress = 0.5 * (1 - np.cos(progress * np.pi))
                    current_x = pan_x * ease_progress
                    current_y = pan_y * ease_progress
                    return (current_x, current_y)
                background_clip = background_clip.with_position(pan_animation)
        
        # 亮度动画
        brightness = config.get("brightness", 1.0)
        if brightness != 1.0:
            def brightness_filter(get_frame, t):
                frame = get_frame(t)
                progress = t / duration
                ease_progress = 0.5 * (1 - np.cos(progress * np.pi))
                current_brightness = 1.0 + (brightness - 1.0) * ease_progress
                bright_frame = frame * current_brightness
                bright_frame = np.clip(bright_frame, 0, 255)
                return bright_frame.astype('uint8')
            background_clip = background_clip.transform(brightness_filter)
        
        # 对比度动画
        contrast = config.get("contrast", 1.0)
        if contrast != 1.0:
            def contrast_animation(t):
                progress = t / duration
                ease_progress = 0.5 * (1 - np.cos(progress * np.pi))
                current_contrast = 1.0 + (contrast - 1.0) * ease_progress
                return current_contrast
            effects.append(vfx.LumContrast(contrast=contrast_animation))
        
        # 应用所有效果
        if effects:
            background_clip = background_clip.with_effects(effects)
        
        return background_clip
    
    def _apply_animation_functions(self, background_clip: VideoClip, animation_functions: Dict[str, Callable], duration: float) -> VideoClip:
        """应用动画函数到视频剪辑"""
        effects = []
        
        # 缩放动画 - 修复缩放中心点问题
        if "zoom" in animation_functions:
            zoom_func = animation_functions["zoom"]
            def zoom_effect(t):
                zoom_factor = zoom_func(t)
                # 确保缩放中心在画面中心
                return zoom_factor
            effects.append(vfx.Resize(zoom_effect))
            print(f"  ✅ 应用缩放动画")

        # 平移动画 - 修复坐标系统
        if "pan" in animation_functions:
            pan_func = animation_functions["pan"]
            def pan_position(t):
                pan_x, pan_y = pan_func(t)
                # 转换为相对于中心的坐标
                return ('center', 'center')  # 暂时固定中心，避免黑边
            # 暂时禁用平移以避免黑边问题
            # background_clip = background_clip.with_position(pan_position)
            print(f"  ⚠️  平移动画已禁用（避免黑边）")
        
        # 亮度动画
        if "brightness" in animation_functions:
            brightness_func = animation_functions["brightness"]
            def brightness_filter(get_frame, t):
                frame = get_frame(t)
                try:
                    current_brightness = brightness_func(t)
                    bright_frame = frame.astype(np.float32) * current_brightness
                    bright_frame = np.clip(bright_frame, 0, 255)
                    return bright_frame.astype('uint8')
                except Exception as e:
                    print(f"⚠️  亮度动画错误: {e}")
                    return frame
            background_clip = background_clip.transform(brightness_filter)
            print(f"  ✅ 应用亮度动画")
        
        # 对比度动画
        if "contrast" in animation_functions:
            effects.append(vfx.LumContrast(contrast=animation_functions["contrast"]))
            print(f"  ✅ 应用对比度动画")
        
        # 饱和度动画
        if "saturation" in animation_functions:
            saturation_func = animation_functions["saturation"]
            def saturation_filter(get_frame, t):
                try:
                    import cv2
                    frame = get_frame(t)
                    current_saturation = saturation_func(t)

                    # 转换为HSV
                    hsv = cv2.cvtColor(frame, cv2.COLOR_RGB2HSV).astype(np.float32)
                    # 调整饱和度通道
                    hsv[:, :, 1] = hsv[:, :, 1] * current_saturation
                    # 确保值在0-255范围内
                    hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 255)
                    # 转回RGB
                    rgb = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2RGB)
                    return rgb
                except Exception as e:
                    print(f"⚠️  饱和度动画错误: {e}")
                    return frame
            background_clip = background_clip.transform(saturation_filter)
            print(f"  ✅ 应用饱和度动画")
        
        # 旋转动画
        if "rotation" in animation_functions:
            effects.append(vfx.Rotate(angle=animation_functions["rotation"]))
            print(f"  ✅ 应用旋转动画")
        
        # 应用所有效果
        if effects:
            background_clip = background_clip.with_effects(effects)
            print(f"  🎬 应用了 {len(effects)} 个动画效果")
        
        return background_clip
    
    def get_available_presets(self) -> Dict[str, List[str]]:
        """获取可用的动画预设"""
        return {
            "real_estate": self.animation_lib.get_real_estate_presets(),
            "cinematic": self.animation_lib.get_cinematic_presets()
        }
    
    def get_preset_description(self, preset_name: str) -> str:
        """获取预设动画的描述"""
        return self.animation_lib.get_animation_description(preset_name)
    
    def suggest_preset_for_scene(self, scene_type: str, duration: float) -> str:
        """为场景推荐合适的动画预设"""
        return self.animation_lib.suggest_animation_for_scene(scene_type, duration)

# 创建全局增强动画渲染器实例
enhanced_animation_renderer = EnhancedAnimationRenderer()

def get_animation_presets_info() -> Dict[str, Any]:
    """获取动画预设信息 - 用于CLI显示"""
    renderer = enhanced_animation_renderer
    presets = renderer.get_available_presets()
    
    info = {
        "categories": {},
        "scene_suggestions": {
            "exterior": "ken_burns - 经典纪录片风格，适合建筑外观",
            "interior": "parallax_up - 视差滚动，展现空间深度",
            "pool": "parallax_down - 向下视差，突出水景效果",
            "bedroom": "luxury_reveal - 豪华揭示，营造温馨氛围",
            "kitchen": "architectural_focus - 建筑焦点，突出设计细节",
            "living_room": "ken_burns - 经典效果，展现居住空间",
            "garden": "parallax_left - 横向视差，展现景观层次",
            "view": "dramatic_zoom - 戏剧缩放，突出景观视野"
        }
    }
    
    for category, preset_list in presets.items():
        info["categories"][category] = {}
        for preset in preset_list:
            info["categories"][category][preset] = renderer.get_preset_description(preset)
    
    return info
