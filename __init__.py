#!/usr/bin/env python3
"""
Reavo Video Creator - 智能视频制作工具
通过配置文件驱动的专业视频生成工具
"""

__version__ = "1.0.0"
__author__ = "Reavo Team"
__description__ = "智能视频制作工具 - 通过配置文件驱动视频生成"

# 导入主要类
from .video_generator import VideoGenerator
from .config_processor import ConfigProcessor, StandardConfig
from .template_renderer import TemplateRenderer
from .resource_manager import ResourceManager

__all__ = [
    'VideoGenerator',
    'ConfigProcessor', 
    'StandardConfig',
    'TemplateRenderer',
    'ResourceManager'
] 